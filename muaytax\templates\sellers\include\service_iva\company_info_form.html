{% load crispy_forms_filters crispy_forms_field %}
{% load custom_filters %}

<div id="company_info_form">
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group row">
                    <label class="col-form-label required-label">Nombre de la Empresa</label>
                    {{ company_form.name|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Tipo de Entidad</label>
                    {{ company_form.legal_entity|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <div class="col-lg-12">
                        <div class="row g-2 mb-2">
                            <div class="col-12">
                                <label class="col-form-label required-label">Direccion de la Empresa</label>
                                {{ company_form.company_address.address|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row g-2 mb-2">
                            <div class="col-6">
                                <label class="col-form-label required-label">Ciudad</label>
                                {{ company_form.company_address.address_city|as_crispy_field }}
                            </div>
                            <div class="col-6">
                                <label class="col-form-label required-label">Provincia / Estado</label>
                                {{ company_form.company_address.address_state|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row g-2 mb-2">
                            <div class="col-4">
                                <label class="col-form-label required-label">Código Postal</label>
                                {{ company_form.company_address.address_zip|as_crispy_field }}
                            </div>
                            <div class="col-8">
                                <label class="col-form-label required-label">País</label>
                                {{ company_form.company_address.address_country|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group row">
                    <div class="col-lg-12">
                        <div class="row g-2 mb-2 d-flex" id="phone_row_wrapper" style="display: flex;">
                            <div id="phone_country_wrapper">
                                <label class="col-form-label required-label">Prefijo telefónico</label>
                                {{ company_form.phone_country|as_crispy_field }}
                            </div>
                            <div id="phone_input_wrapper">
                                <label class="col-form-label required-label">Teléfono</label>
                                {{ company_form.phone_national|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="form-group row">
                    <label class="col-form-label required-label">Tipo de actividad</label>
                    {{ company_form.activity_type|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Productos / Servicios</label>
                    {{ company_form.products_and_services|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Descripción de la actividad principal</label>
                    {{ company_form.desc_main_activity|as_crispy_field }}
                </div>

                <div class="form-group row">
                    <label class="col-form-label required-label">Dirección de Amazon (VIES)</label>
                    {{ company_form.amazon_vies_screenshot|as_crispy_field }}
                    <p id="amazon_vies_screenshot_fileinfo" class="text-muted"></p>
                </div>
                <div class="form-group row">
                    <label class="col-form-label required-label">Escrituras de la Empresa</label>
                    {{ company_form.business_deeds|as_crispy_field }}
                    <p id="business_deeds_fileinfo" class="text-muted"></p>
                </div>

                {% if legal_entity == "sl" %}
                    <div class="form-group row">
                        <label class="col-form-label required-label">Registro Mercantil (SL)</label>
                        {{ company_form.mercantile_registry|as_crispy_field }}
                        <p id="mercantile_registry_fileinfo" class="text-muted"></p>
                    </div>
                {% elif legal_entity == "llc" %}
                    <div class="form-group row">
                        <label class="col-form-label required-label">Registro Mercantil / Certificado LLC*</label>
                        {{ company_form.business_registry|as_crispy_field }}
                        <p id="business_registry_fileinfo" class="text-muted"></p>
                    </div>

                    <div class="form-group row">
                        <label class="col-form-label required-label">Certificate of Comparison (LLC)</label>
                        {{ company_form.comparison_certificate|as_crispy_field }}
                        <p id="comparison_certificate_fileinfo" class="text-muted"></p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{{ company_info_data|json_script:"json-company-info" }}
{{ all_product_services_json|json_script:"json-all-options" }}
{{ validation_company_info|json_script:"json-validation" }}

<script>
    methodCompany = {
        // === Funciones para el campo de productos/servicios ===
        filterProductServices(productsSelect, allOptions, type, selectedValue = "") {
            window.debugLog("[f -> filterProductServices() ] Aplica filtro de productos según tipo de actividad");
            productsSelect.innerHTML = `<option value="">-----</option>`;
            if (!type) {
                productsSelect.disabled = true;
                return;
            }

            const filtered = allOptions.filter(opt => opt.product_service_type === type);
            filtered.forEach(opt => {
                const option = document.createElement("option");
                option.value = opt.code;
                option.textContent = `${opt.code} | ${opt.description}`;
                if (opt.code === selectedValue) option.selected = true;
                productsSelect.appendChild(option);
            });

            productsSelect.disabled = false;
        },

        // === Funciones para prefijos telefonicos ===
        togglePhoneField(prefixSelect, phoneInput) {
            window.debugLog("[f -> togglePhoneField() ] Habilita o limpia el input de teléfono según el prefijo");
            if (!phoneInput) return;
            phoneInput.disabled = !prefixSelect.value;
            if (!prefixSelect.value) phoneInput.value = "";
        },

        adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone) {
            window.debugLog("[f -> adjustPrefixFlexWidth() ] Ajusta el ancho relativo del prefijo y numero de telefono según el texto");

            const selectedOption = prefixSelect.options[prefixSelect.selectedIndex];
            if (!selectedOption) return;

            const span = document.createElement("span");
            span.style.visibility = "hidden";
            span.style.position = "absolute";
            span.style.whiteSpace = "nowrap";
            span.style.fontSize = getComputedStyle(prefixSelect).fontSize;
            span.style.fontFamily = getComputedStyle(prefixSelect).fontFamily;
            span.innerText = selectedOption.textContent;
            document.body.appendChild(span);
            const textWidth = span.offsetWidth + 80;
            document.body.removeChild(span);

            const fullRowWidth = inputPhone.offsetWidth;
            let percent = (textWidth / fullRowWidth) * 100;
            percent = Math.max(25, Math.min(percent, 80));
            const rest = 100 - percent;

            prefixWrapper.style.flexBasis = `${percent}%`;
            phoneWrapper.style.flexBasis = `${rest}%`;

            window.debugLog(`[DYNAMIC WIDTH] Total: ${fullRowWidth}px | Prefijo: ${percent.toFixed(1)}%`);
        },

        initPhoneFields(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone) {
            window.debugLog("[f -> initPhoneFields() ] Inicializa el campo de prefijo y número de teléfono");

            this.togglePhoneField(prefixSelect, phoneInput);
            this.adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone);
        },

        // === Funciones de carga inicial ===
        loadInitialValues(data) {
            window.debugLog("[f -> loadInitialValues() ] Carga los valores iniciales del formulario desde el backend");
            for (const [key, value] of Object.entries(data)) {
                if (value && typeof value === "object" && "file" in value) {
                    const fileInfo = document.getElementById(`${key}_fileinfo`);
                    if (fileInfo && value.value) {
                        fileInfo.innerHTML = `Documento cargado: <a href="${value.file}" target="_blank">${value.value}</a>`;
                        fileInfo.classList.remove("d-none");
                    }
                    continue;
                }

                const input = document.querySelector(`[name="${key}"], #id_${key}`); 
                if (!input || typeof value === "object") continue;

                if (input.tagName === "SELECT") {
                    input.value = value;
                } else if (input.type === "checkbox") {
                    input.checked = Boolean(value);
                } else {
                    input.value = value;
                }
            }
        },

        initProductField(activityType, allOptions, productsSelect, selectedCode) {
            window.debugLog("[f -> initProductField() ] Carga productos según actividad inicial");

            if (activityType) {
                this.filterProductServices(productsSelect, allOptions, activityType, selectedCode);
            }
            productsSelect.disabled = !selectedCode;
        },

        // === Inicializa prefijo y número si vienen vacíos desde el backend ===
        initializePhoneFromAddress(addressCountrySelect, prefixSelect, phoneInput, data) {
            window.debugLog("[f -> initializePhoneFromAddress() ] Inicializa valores reactivos desde address_country y contact_phone");

            const addressCountry = addressCountrySelect?.value || "";
            const phoneCountry = data?.phone_country || "";
            const contactPhone = data?.contact_phone || "";

            console.log(`address_country: ${addressCountry}, phone_country: ${phoneCountry}, contact_phone: ${contactPhone}`);

            // Si no hay prefijo pero sí país de dirección → usarlo
            if (!phoneCountry && addressCountry) {
                window.debugLog(`→ Se usará address_country (${addressCountry}) como phone_country`);
                prefixSelect.value = addressCountry;
                const changeEvent = new Event("change");
                prefixSelect.dispatchEvent(changeEvent); // Forzamos render y ajuste
            }

            // Si hay número real y prefijo → extraer parte nacional
            if (prefixSelect.value && contactPhone) {
                const selectedOption = prefixSelect.options[prefixSelect.selectedIndex];
                const rawText = selectedOption?.textContent || "";
                const prefixMatch = rawText.match(/\(\s*(\+\d+)\s*\)/);
                const prefix = prefixMatch ? prefixMatch[1] : "";

                if (prefix && contactPhone.startsWith(prefix)) {
                    const nationalNumber = contactPhone.slice(prefix.length).trim();
                    window.debugLog(`→ Se extrae número nacional: ${nationalNumber}`);
                    phoneInput.value = nationalNumber;
                } else {
                    window.debugLog(`[WARN] No se pudo extraer el prefijo de ${contactPhone} usando ${rawText}`);
                }
            }
        },
        // === Funciones de validacion y mensajes del gestor ===
        displayValidationMessages(validationData) {
            window.debugLog("[f -> displayValidationMessages() ] Inserta los mensajes de error del validador del gestor");
            if (!validationData) return;
            
            const allowedFields = [];

            for (const [fieldName, validation] of Object.entries(validationData)) {
                if (validation.status === "incorrecto" && validation.pending) {
                    allowedFields.push(fieldName);
                    const input = document.querySelector(`[name="${fieldName}"], #id_${fieldName}`); 
                    if (!input) continue;

                    input.classList.add("error-coment");

                    if (!input.parentElement.querySelector(".manager-card-comment")) {
                        const container = document.createElement("div");
                        container.className = "manager-card-comment mt-2";
                        container.innerHTML = `
                            <div class="manager-card-title">Comentario del gestor:</div>
                            <div class="manager-card-text">${validation.comment || "Campo con error"}</div>
                        `;
                        const fileInfo = document.getElementById(`${fieldName}_fileinfo`);
                        (fileInfo || input.parentElement).appendChild(container);
                    }
                }
            }

            if (!isNewForm) {
                window.debugLog("Bloqueando campos con errores...");
                lockCompanyInfoFields(allowedFields);
            }
        },
    }

    debuggerCompany = {
        // === Funciones para debugger ===
        debugFormFields(formId) {
            window.debugLog("[f -> debugFormFields() ] Imprime los valores actuales del formulario", formId);
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll("input, select, textarea");
            window.debugLog("[DEBUG FORM] Valores actuales:");
            inputs.forEach(input => {
                const id = input.id || "(sin id)";
                const name = input.name || "(sin name)";
                const value = input.type === "checkbox" ? input.checked : input.value;
                window.debugLog(`>> ${id} [name="${name}"] → ${value}`);
            });
        },
    }

    handlersCompany = {
        // === Funciones obsevadoras ===
        observeAddressCountryForPhonePrefix(addressCountrySelect, prefixSelect, phoneInput, data) {
            window.debugLog("[f -> observeAddressCountryForPhonePrefix() ] Observa cambios en address_country para autocompletar phone_country");

            if (!addressCountrySelect) {
                console.warn("Elemento 'addressCountrySelect' no encontrado.");
                return;
            }

            addressCountrySelect.addEventListener("change", function () {
                if (!prefixSelect.value) {
                    window.debugLog("→ phone_country vacío tras cambio en address_country. Reejecutando inicialización de prefijo...");
                    methodCompany.initializePhoneFromAddress(addressCountrySelect, prefixSelect, phoneInput, data);
                } else {
                    window.debugLog("→ phone_country ya tiene valor. No se autocompleta.");
                }
            });
        },

        // === Fuciones manejadoras de eventos ===
        handleActivityTypeChange(productsSelect, allOptions) {
            window.debugLog("[f -> handleActivityTypeChange() ] Maneja el cambio de tipo de actividad");
            return function () {
                methodCompany.filterProductServices(productsSelect, allOptions, this.value);
            };
        },

        handlePrefixChange(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone) {
            window.debugLog("[f -> handlePrefixChange() ] Maneja el cambio de prefijo telefónico");
            return function () {
                methodCompany.togglePhoneField(prefixSelect, phoneInput);
                methodCompany.adjustPrefixFlexWidth(prefixSelect, phoneInput, prefixWrapper, phoneWrapper, inputPhone);
            };
        },
    }
    
    window.CompanyFormModule = {
        methods: methodCompany,
        debugger: debuggerCompany,
        handlers: handlersCompany,
    }

    // ========== main.js (Registro de eventos del DOM) ==========
    document.addEventListener("DOMContentLoaded", function () {
        
        // Variables de contexto
        const context = {
            // data: JSON.parse(document.getElementById("json-company-info").textContent),
            data: {{ company_info_data|safe }},
            allOptions: JSON.parse(document.getElementById("json-all-options").textContent),
            validation: JSON.parse(document.getElementById("json-validation").textContent),
        };

        console.log("[DEBUG] Contexto cargado:", context.data);
        // variables del DOM
        const dom = {
            activityTypeSelect: document.getElementById("id_activity_type"),
            productsSelect: document.getElementById("id_products_and_services"),
            addressCountrySelect: document.getElementById("id_company_address_address_country"),
            prefixSelect: document.getElementById("id_phone_country"),
            phoneInput: document.getElementById("id_phone_national"),
            prefixWrapper: document.getElementById("phone_country_wrapper"),
            phoneWrapper: document.getElementById("phone_input_wrapper"),
            inputPhone: document.getElementById("phone_row_wrapper"),
        };

        // Eventos
        dom.activityTypeSelect?.addEventListener( "change", window.CompanyFormModule.handlers.handleActivityTypeChange(dom.productsSelect, context.allOptions));
        dom.prefixSelect?.addEventListener( "change", window.CompanyFormModule.handlers.handlePrefixChange(dom.prefixSelect, dom.phoneInput, dom.prefixWrapper, dom.phoneWrapper, dom.inputPhone));

        // Inicialización
        CompanyFormModule.methods.initPhoneFields(dom.prefixSelect, dom.phoneInput, dom.prefixWrapper, dom.phoneWrapper, dom.inputPhone);
        CompanyFormModule.methods.initProductField( dom.activityType, context.allOptions, dom.productsSelect, dom.productsSelect.value);
        CompanyFormModule.methods.loadInitialValues(context.data);
        CompanyFormModule.methods.initializePhoneFromAddress(dom.addressCountrySelect, dom.prefixSelect, dom.phoneInput, context.data);
        CompanyFormModule.methods.displayValidationMessages(context.validation);
        CompanyFormModule.handlers.observeAddressCountryForPhonePrefix(dom.addressCountrySelect, dom.prefixSelect, dom.phoneInput, context.data);
        // Debug
        window.debugLog("[DEBUG] Inicializando campos del formulario de empresa");
        window.CompanyFormModule.debugger.debugFormFields("company_info_form");});
</script>
