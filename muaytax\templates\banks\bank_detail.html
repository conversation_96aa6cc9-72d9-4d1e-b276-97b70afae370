{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}

{% block title %}Bancos{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/credit-card/credit-card.css" />
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">Bancos</h5>
          </div>
          <ul class="breadcrumb">
            {% if user.role == 'manager' %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">Vendedores</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_banks:bank_list' seller.shortname  %}">Bancos</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Detalles del Banco</a>
            </li>
            {% else %}
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_banks:bank_list' seller.shortname  %}">Bancos</a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">Detalles del Banco</a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="vue">
    {% if form.errors %}
    <div class="alert alert-danger mb-4" role="alert">
      <h4 class="alert-heading">Error de validación</h4>
      <p>Por favor, corrija los siguientes errores:</p>
      <ul>
        {% for field in form %}
          {% for error in field.errors %}
            <li><strong>{{ field.label }}:</strong> {{ error }}</li>
          {% endfor %}
        {% endfor %}
        {% for error in form.non_field_errors %}
          <li>{{ error }}</li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <div v-if="bank_type && bank_type == 'bankaccount'" class="mb-4">
      <form class="form-horizontal" id="form" method="post" enctype="multipart/form-data" action="" style="display:none;" v-show="true">
        {% csrf_token %}
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-2"><!-- Empty COL --></div>
                <!-- contenido  -->
                <div class="col">
                  <div class="row">
                    <!-- form -->
                    <div class="col form-wrapper">
                      <div class="row">
                        <!-- Bank Account Type: Bankt Account -->
                        <div class="col-12 mb-3" v-show="bank_type && bank_type == 'bankaccount'" >
                          <h1>Cuenta Bancaria <i class="fa-solid fa-building-columns"></i></h1>
                        </div>

                        <!-- Bank Account Type: Other -->
                        <div id="div_id_bank_account_type" class="col-12 mb-3" style="display:none;" v-show="!bank_type">
                          <label for="id_bank_account_type" class="form-label requiredField">
                            Tipo de Cuenta<span class="asteriskField">*</span>
                          </label>
                          <select
                            name="bank_account_type"
                            class="select form-select"
                            id="id_bank_account_type"
                            v-model="bank_type"
                            required
                          >
                                <option value="" selected="">---------</option>
                                <option value="bankaccount">Cuenta Bancaria</option>
                                <option value="creditcard">Tarjeta de Credito</option>
                          </select>
                        </div>

                        <!-- Bank Name -->
                        <div id="div_id_bank_name" class="col-12 mb-3">
                          <label for="id_bank_name" class="form-label requiredField">
                            Nombre del Banco <span class="asteriskField">*</span>
                          </label>
                          <input
                            type="text"
                            name="bank_name"
                            maxlength="100"
                            class="textinput textInput form-control"
                            id="id_bank_name"
                            v-model="bank_name"
                            required
                          >
                        </div>

                        <!-- Bank IBAN -->
                        <div id="div_id_bank_iban" class="col-12 mb-3" v-if="bank_type && bank_type == 'bankaccount'">
                          <label for="id_bank_iban" class="form-label requiredField">
                            IBAN <span class="asteriskField">*</span>
                          </label>
                          <input
                            type="text"
                            name="bank_iban"
                            maxlength="34"
                            class="textinput textInput form-control"
                            id="id_bank_iban"
                            v-model="bank_iban"
                            required
                          >
                        </div>

                        <!-- Bank Accounting Account -->
                        <div id="div_id_bank_accounting_account" class="col-6 mb-3">
                          <label for="id_bank_accounting_account" class="form-label requiredField">
                                Cuenta Contable
                          </label>
                          <input
                            readonly
                            type="text"
                            name="bank_accounting_account"
                            maxlength="9"
                            minlength="9"
                            class="textinput textInput form-control"
                            id="id_bank_accounting_account"
                            required
                          >
                        </div>

                        <!-- Bank Initial Ammount -->
                        <div id="div_id_bank_initial_amount" class="col mb-3" v-if="bank_type && bank_type == 'bankaccount'">
                          <label for="id_bank_initial_amount" class="form-label requiredField">
                            Monto Inicial <span class="asteriskField">*</span>
                          </label>
                          <input
                            type="number"
                            name="bank_initial_amount"
                            step="0.01"
                            class="numberinput form-control"
                            id="id_bank_initial_amount"
                            v-model="bank_initial_amount"
                            required
                          >
                        </div>

                        <!-- Bank Currency -->
                        <div id="div_id_bank_currency" class="col mb-3">
                          <label for="id_bank_initial_amount" class="form-label requiredField">
                            Moneda <span class="asteriskField">*</span>
                          </label>
                          <select name="bank_currency" class="select form-select form-control" id="id_bank_currency" v-model="bank_currency" required>
                            {% for cur in currency %}
                              <option value="{{cur.code}}">{{cur.description}}</option>
                            {% endfor %}
                            {% comment %} <option value="USD">EE.UU. Dólar</option> <option value="GBP">Gran Bretaña Libra Esterlina</option> {% endcomment %}
                          </select>
                        </div>

                      </div>
                      <div class="row mt-3">
                        <div class="control-group">
                          {% if object.pk is not None %}
                            <button style="width: 100%;" type="submit" class="btn btn-primary" id="submitUpload">Actualizar</button>
                          {% else %}
                            <button style="width: 100%;" type="submit" class="btn btn-primary" id="submit">Guardar</button>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                    <!-- form -->
                  </div>
                </div>
                <!-- contenido  -->
                <div class="col-2"><!-- Empty COL --></div>
              </div>
            </div>
          </div>
      </form>
    </div>

    <div v-if="bank_type && bank_type == 'creditcard'" class="container">
      <div>
        <div class="row g-4 pt-3">
          <!-- Card Preview Section (Order changes on mobile) -->
          <div class="col-lg-6 order-lg-1 order-2">
            <div class="credit-card-container">
              <div class="credit-card" id="creditCard">
                <div class="credit-card-front">
                  <div
                    :class="['credit-card-background', bank_credit_card_entity ? bank_credit_card_entity.toLowerCase() + '-bg' : 'bg-card-default']" id="cardBackground"></div>
                  <div class="credit-card-overlay"></div>
                  <div class="credit-card-content">
                    <div class="bank-name-badge" id="bankNameDisplay">[[displayBankName]]</div>

                    <div class="d-flex gap-2 align-items-center">
                      <img src="{% static 'assets/images/banks/card-logos/chip.png' %}" alt="logo" width="35" height="25">
                      <img class="nfc-icon-color" src="https://i.ibb.co/j68MMps/nfc.png" alt="logo" width="15" height="15">
                    </div>

                    <div>
                      <div class="credit-card-label">
                        CARD NUMBER
                      </div>
                      <div class="credit-card-number">
                        [[displayBankNumber]]
                      </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center position-relative">
                      <div class="flex-grow-1 d-flex flex-column justify-content-center">
                        <div class="credit-card-label">
                          CARD HOLDER
                        </div>
                        <div class="credit-card-holder-name">
                          [[dj.seller.name]]
                        </div>
                      </div>
                      <div class="credit-card-logo">
                        <img :class="{ 'd-none': !creditCardLogo }" :src="creditCardLogo" alt="logo" width="60" height="60">
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Form Section -->
          <div class="col-lg-6 order-lg-2 order-1">
            <h2 class="mb-4 fw-bold">
              <span class="text-mu-dark">Tarjeta de Crédito</span>
              <i class="fas fa-credit-card ms-2 text-mu-dark"></i>
            </h2>
            <div class="credit-card-form-container">
              <form @submit="onFormSubmit" method="post" enctype="multipart/form-data" action="">
                {% csrf_token %}
                <input type="hidden" name="bank_account_type" value="creditcard">
                <!-- Bank Name -->
                <div class="mb-4">
                  <div class="form-floating position-relative">
                    <i class="fas fa-university credit-card-input-icon"></i>
                    <input
                      type="text"
                      id="id_bank_name"
                      name="bank_name"
                      maxlength="50"
                      class="form-control ps-5"
                      v-model="bank_name"
                      placeholder="Nombre del Banco"
                      required
                      :readonly="submittingForm">
                    <label for="id_bank_name">Nombre del Banco *</label>
                  </div>
                  <div class="invalid-feedback" id="bankNameError"></div>
                </div>
                <!-- Bank Credit Card Number -->
                <div class="mb-4">
                  <div class="form-floating position-relative">
                    <i class="fas fa-credit-card credit-card-input-icon"></i>
                    <input
                      type="text"
                      id="id_bank_credit_card"
                      name="bank_credit_card"
                      maxlength="16"
                      pattern="[0-9]{13}(?:[0-9]{2})?(?:[0-9]{1})?"
                      class="form-control ps-5"
                      placeholder="1234 5678 9012 3456"
                      v-model="bank_credit_card"
                      @input="handlesCreditCardNumberInput"
                      inputmode="numeric"
                      required
                      :readonly="submittingForm">
                    <label for="id_bank_credit_card">Nº Tarjeta de Crédito *</label>
                  </div>
                  <div class="invalid-feedback" id="cardNumberError"></div>
                </div>
                <!-- Bank Accounting Account and currency -->
                <div class="row g-4 mb-4">
                  <!-- Bank Accounting Account -->
                  <div class="col-md-6">
                    <div class="form-floating position-relative">
                      <i class="fas fa-user credit-card-input-icon"></i>
                      <input
                        readonly
                        type="text"
                        id="id_bank_accounting_account"
                        name="bank_accounting_account"
                        maxlength="9"
                        minlength="9"
                        class="form-control ps-5"
                        required
                        :readonly="submittingForm">
                      <label for="id_bank_accounting_account">Cuenta Contable</label>
                    </div>
                    <div class="invalid-feedback" id="accountNumberError"></div>
                  </div>
                  <!-- Bank Currency -->
                  <div class="col-md-6">
                    <div class="form-floating position-relative">
                      <i class="fas fa-globe credit-card-input-icon"></i>
                      <select
                        id="id_bank_currency"
                        name="bank_currency"
                        v-model="bank_currency"
                        class="form-select ps-5"
                        required
                        readonly
                        :disabled="submittingForm">
                        {% for cur in currency %}
                          <option value="{{cur.code}}">{{cur.description}}</option>
                        {% endfor %}
                      </select>
                      <input 
                          v-if="submittingForm"
                          type="hidden"
                          name="bank_currency"
                          :value="bank_currency">
                      <label for="currency">Moneda *</label>
                    </div>
                  </div>
                </div>
                {% if object.pk is not None %}
                  <button
                    style="width: 100%;"
                    type="submit"
                    class="btn btn-primary w-100 py-3 fw-bold"
                    id="submitUpload"
                    :disabled="submittingForm">
                    <span v-if="!submittingForm">
                      <i class="fas fa-check me-2"></i>
                      Actualizar Tarjeta
                    </span>
                    <span v-else>
                      <i class="fas fa-spinner fa-spin me-2"></i>
                      Actualizando...
                    </span>
                  </button>
                {% else %}
                  <button
                    style="width: 100%;"
                    type="submit"
                    class="btn btn-primary w-100 py-3 fw-bold"
                    :disabled="submittingForm">
                    <span v-if="!submittingForm">
                      <i class="fas fa-check me-2"></i>
                      Guardar Tarjeta
                    </span>
                    <span v-else>
                      <i class="fas fa-spinner fa-spin me-2"></i>
                      Guardando...
                    </span>
                  </button>
                {% endif %}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <br>
  </div>
{% endblock content %}

{% block javascripts %}
  <!-- VUE3 JS  -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function() {
        var accountingAccountInput = document.getElementById('id_bank_accounting_account');
        accountingAccountInput.value = "{{ accounting_account_code }}";
    });
  </script>

  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/js/plugins/credit-card/credit-card.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <script>

    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const { ref, watch } = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const dj = ref({});
    const bank_type = ref(null);
    const bank_name = ref('');
    const bank_accounting_account = ref('');
    const bank_iban = ref('');
    const bank_initial_amount = ref(0);
    const bank_credit_card = ref('');
    const bank_credit_card_entity = ref(null);
    const bank_currency = ref('EUR');
    const submittingForm = ref(false);


    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const safeDjangoJson = {{ json | safe }};
    const getDjangoData = (djObj=null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1 ) {
          djObj = JSON.parse(JSON.stringify(safeDjangoJson));
        }
        if (djObj != null) {
          // console.log("djObj: ", djObj);
          let dj2 = {};
          for (const [key,value] of Object.entries(djObj) ) {
            dj2[key] = [];
            for (const obj of JSON.parse(value) ) {
              dj2[key].push({ ...obj?.fields , "pk":obj?.pk })
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      // console.log(dj.value);
    };

    const getCCEntity = () => {
      bank_credit_card.value = bank_credit_card.value.trim().replace(' ','').replace('-','');
      const ccnumber = bank_credit_card.value;
      const regexVISA1 = '4[0-9]{12}';
      const regexVISA2 = '4[0-9]{15}';
      const regexMasterCard = '(?:5[1-5][0-9]{2}|222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}';
      const regexAmericanExpress = '3[47][0-9]{13}';
      const regexDiscovery = '6(?:011|5[0-9]{2})[0-9]{12}';
      const regexJCB = '(?:2131|1800|35\d{3})\d{11} ';
      let isOK = false;

      bank_credit_card_entity.value = null;

      if ((ccnumber.match(regexVISA1) && ccnumber.length == 13) || (ccnumber.match(regexVISA2) && ccnumber.length == 16) ) {
        bank_credit_card_entity.value = 'VISA';
      } else if (ccnumber.match(regexMasterCard) && ccnumber.length == 16) {
        bank_credit_card_entity.value = 'MasterCard';
      } else if (ccnumber.match(regexAmericanExpress) && ccnumber.length == 15) {
        bank_credit_card_entity.value = 'AmericanExpress';
      } else if (ccnumber.match(regexDiscovery) && ccnumber.length == 16) {
        bank_credit_card_entity.value = 'Discovery';
      } else if (ccnumber.match(regexJCB) && ccnumber.length == 16) {
        bank_credit_card_entity.value = 'JCB';
      } else {
        bank_credit_card_entity.value = null;
      }

      return bank_credit_card_entity.value;
    };

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    if (dj.value?.banktype?.length > 0) {
      bank_type.value = dj.value.banktype[0].pk;
    }

    if (dj.value?.bank?.length > 0) {
      bank_name.value = dj.value.bank[0].bank_name;
      //bank_accounting_account.value = dj.value.bank[0].bank_accounting_account;
      bank_iban.value = dj.value.bank[0].bank_iban;
      bank_initial_amount.value = dj.value.bank[0].bank_initial_amount;
      bank_credit_card.value = dj.value.bank[0].bank_credit_card;
      bank_currency.value = dj.value.bank[0].bank_currency;
      if (bank_type.value == 'creditcard') { getCCEntity(); }
    }

    // RECUPERAR DATOS DEL FORMULARIO EN CASO DE ERROR /////////////////////////////////
    // Esta función recupera los valores enviados que podrían haberse perdido debido a errores
    const recoverFormValues = () => {
      // Verificar si hay errores de formulario (esto lo establece Django en el contexto)
      const hasFormErrors = {% if has_form_errors %}true{% else %}false{% endif %};

      if (hasFormErrors) {
        // Recuperar valores del POST que podrían estar en los campos Form Django
        // Usando el back reference para obtener valores del formulario Django
        const formBankName = document.querySelector('[name="bank_name"]');
        const formBankIban = document.querySelector('[name="bank_iban"]');
        const formBankInitialAmount = document.querySelector('[name="bank_initial_amount"]');
        const formBankCreditCard = document.querySelector('[name="bank_credit_card"]');
        const formBankCurrency = document.querySelector('[name="bank_currency"]');
        const formBankType = document.querySelector('[name="bank_account_type"]');

        // Actualizar el estado de Vue con los valores del formulario, si existen
        if (formBankName && formBankName.value) {
          bank_name.value = formBankName.value;
        }

        if (formBankIban && formBankIban.value) {
          bank_iban.value = formBankIban.value;
        }

        if (formBankInitialAmount && formBankInitialAmount.value) {
          bank_initial_amount.value = formBankInitialAmount.value;
        }

        if (formBankCreditCard && formBankCreditCard.value) {
          bank_credit_card.value = formBankCreditCard.value;
          getCCEntity(); // Actualizar la entidad de la tarjeta
        }

        if (formBankCurrency && formBankCurrency.value) {
          bank_currency.value = formBankCurrency.value;
        }

        if (formBankType && formBankType.value) {
          bank_type.value = formBankType.value;
        }
      }
    };

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      bank_type,
      bank_name,
      bank_accounting_account,
      bank_iban,
      bank_initial_amount,
      bank_credit_card,
      bank_credit_card_entity,
      bank_currency,
      getCCEntity,
      submittingForm,
      recoverFormValues,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const { createApp } = VUE3;
      const app = createApp({
          components: {
            EasyDataTable: window["vue3-easy-data-table"],
          },
          delimiters: ['[[', ']]'],
          el: target,
          data() { return { ...data_export } },
          methods: {
            handlesCreditCardNumberInput(event){
              this.bank_credit_card = event.target.value.replace(/\D/g, '');
              this.getCCEntity();
            },
            maskCardNumber(cardNumber) {
              if (!cardNumber) return '';

              const clean = cardNumber.replace(/\D/g, ''); // remove non-digits
              const groups = clean.match(/.{1,4}/g) || [];

              const maskedGroups = groups.map((group, index) => {
                if (index === groups.length - 1) {
                  return group; // show last group
                }
                return '*'.repeat(group.length); // mask previous groups
              });
              return maskedGroups.join(' '); // join groups with space
            },
            validateBankName() {
              const errors = [];
              if (!this.bank_name || this.bank_name.trim() === '') {
                errors.push('El nombre del banco no puede estar vacío.');
              }
              if (this.bank_name && !/^[\w\s\.\,\-\(\)\/ñÑáéíóúÁÉÍÓÚüÜ]+$/.test(this.bank_name)) {
                errors.push('El nombre del banco contiene caracteres no permitidos. Solo se permiten letras, números, espacios, símbolos básicos (.,-()/), la ñ y vocales acentuadas.');
              }
              return errors;
            },
            validateIban() {
              const errors = [];
              if (!this.bank_iban || this.bank_iban.trim() === '') {
                errors.push('El IBAN no puede estar vacío.');
              }

              const iban = (this.bank_iban || '').replace(/\s/g, '').toUpperCase();
              if (iban && !/^[A-Z0-9]+$/.test(iban)) {
                errors.push('El IBAN solo puede contener letras y números.');
              }

              if (iban && iban.length < 15) {
                errors.push('El IBAN debe tener al menos 15 caracteres.');
              }

              return errors;
            },
            validateInitialAmount() {
              const errors = [];
              const amount = parseFloat(this.bank_initial_amount);

              if (this.bank_initial_amount === '' || this.bank_initial_amount === null) {
                errors.push('El monto inicial no puede estar vacío.');
              } else if (isNaN(amount)) {
                errors.push('El monto inicial debe ser un número válido.');
              } else if (amount < 0) {
                errors.push('El monto inicial no puede ser negativo.');
              }

              return errors;
            },
            validateCreditCardNumber() {
              const errors = [];
              const ccnumber = this.bank_credit_card ? this.bank_credit_card.replace(/\D/g, '') : '';

              if (!ccnumber) {
                errors.push('El número de tarjeta no puede estar vacío.');
                return errors;
              }

              if (ccnumber.length < 13 || ccnumber.length > 16) {
                errors.push('El número de tarjeta debe tener entre 13 y 16 dígitos.');
              }

              // Luhn algorithm
              let sum = 0;
              let shouldDouble = false;
              for (let i = ccnumber.length - 1; i >= 0; i--) {
                let digit = parseInt(ccnumber.charAt(i), 10);
                if (shouldDouble) {
                  digit *= 2;
                  if (digit > 9) digit -= 9;
                }
                sum += digit;
                shouldDouble = !shouldDouble;
              }
              if (sum % 10 !== 0) {
                errors.push('El número de tarjeta no es válido según el algoritmo de Luhn.');
              }

              return errors;
            },
            getValidationErrors() {
              let errors = [
                ...this.validateBankName(),
              ];

              if (this.bank_type === 'bankaccount') {
                errors = errors.concat(this.validateIban());
                errors = errors.concat(this.validateInitialAmount());
              } else if (this.bank_type === 'creditcard') {
                errors = errors.concat(this.validateCreditCardNumber());
              }

              return errors;
            },
            onFormSubmit(event) {
              this.submittingForm = true;

              const errors = this.getValidationErrors();

              if (errors.length > 0) {
                Swal.fire({
                  title: 'Error de validación',
                  html: `<ul class="text-start"><li>${errors.join('</li><li>')}</li></ul>`,
                  icon: 'error',
                  confirmButtonText: 'Entendido'
                });
                event.preventDefault();
                this.submittingForm = false;
                return;
              }


            }
          },
          computed: {
            formattedCreditCardNumber() {
              if (!this.bank_credit_card) return '';
              let value = this.bank_credit_card.replace(/\D/g, '');
              return value.replace(/(.{4})/g, '$1 ').trim(); // adds space every 4 digits
            },
            displayBankNumber() {
              return this.bank_credit_card ? this.maskCardNumber(this.formattedCreditCardNumber) : '**** **** **** ****';
            },
            displayBankName() {
              return this.bank_name ? this.bank_name : 'Nombre del Banco';
            },
            creditCardLogo() {
              switch (this.bank_credit_card_entity) {
                case 'VISA':
                    return '{% static "assets/images/banks/card-logos/visa.png" %}';
                case 'MasterCard':
                    return '{% static "assets/images/banks/card-logos/mastercard.png" %}';
                case 'AmericanExpress':
                    return '{% static "assets/images/banks/card-logos/american.png" %}';
                case 'Discovery':
                    return '{% static "assets/images/banks/card-logos/discover.png" %}';
                case 'JCB':
                    return '{% static "assets/images/banks/card-logos/jcb.png" %}';
                default:
                    return null;
              }
            }
          },
          mounted() {
            // Llamamos a la función para recuperar valores en caso de errores al cargar el componente
            this.recoverFormValues();
          }
      });
      app.mount(target);
    };
    createVue3('.vue', data_export);

    // Muestra mensajes de error del formulario usando SweetAlert2
    document.addEventListener('DOMContentLoaded', function() {
      // Verifica si hay errores de formulario del servidor
      const form = document.querySelector('form');

      // Buscar errores del servidor en diferentes formatos
      const errorElements = form.querySelectorAll('.is-invalid, .invalid-feedback, .errorlist, ul.errorlist li');

      if (errorElements.length > 0) {
        // Prepara un mensaje con los errores
        let errorMessage = '<ul class="text-start">';
        errorElements.forEach(element => {
          if (element.textContent.trim() !== '') {
            errorMessage += `<li>${element.textContent.trim()}</li>`;
          }
        });
        errorMessage += '</ul>';

        // Si hay errores, muestra el SweetAlert
        if (errorMessage !== '<ul class="text-start"></ul>') {
          Swal.fire({
            title: 'Error de validación',
            html: errorMessage,
            icon: 'error',
            confirmButtonText: 'Entendido'
          });
        }
      }

      // Intercepta la validación del formulario
      // form.addEventListener('submit', function(event) {
      //   let isValid = true;
      //   let errorMessages = [];

      //   // Validar IBAN si es una cuenta bancaria
      //   const bankTypeElement = document.getElementById('id_bank_account_type');
      //   const bankType = bankTypeElement ? bankTypeElement.value : '{{ banktype.code }}';

      //   // Validar nombre del banco (permitiendo espacios pero haciendo trim)
      //   const bankNameElement = document.getElementById('id_bank_name');
      //   if (bankNameElement) {
      //     const bankName = bankNameElement.value.trim();
      //     if (!bankName) {
      //       isValid = false;
      //       errorMessages.push('El nombre del banco no puede estar vacío después de eliminar espacios.');
      //     }
      //     // Validar que solo contiene caracteres permitidos (alfanuméricos, espacios y algunos símbolos)
      //     else if (!/^[\w\s\.\,\-\(\)\/]+$/.test(bankName)) {
      //       isValid = false;
      //       errorMessages.push('El nombre del banco contiene caracteres no permitidos. Solo se permiten letras, números, espacios y símbolos básicos (.,-()/)).');
      //     }
      //   }

      //   if (bankType === 'bankaccount') {
      //     // Validar IBAN
      //     const ibanElement = document.getElementById('id_bank_iban');
      //     if (ibanElement) {
      //       const iban = ibanElement.value.trim();
      //       const cleanedIban = iban.replace(/\s/g, '');  // Eliminar TODOS los espacios
      //       if (!cleanedIban) {
      //         isValid = false;
      //         errorMessages.push('El IBAN es obligatorio para cuentas bancarias.');
      //       } else if (cleanedIban.length < 15) {
      //         isValid = false;
      //         errorMessages.push('El IBAN debe tener al menos 15 caracteres.');
      //       }
      //       // Validar formato de IBAN (caracteres alfanuméricos)
      //       else if (!/^[a-zA-Z0-9]+$/.test(cleanedIban)) {
      //         isValid = false;
      //         errorMessages.push('El IBAN solo puede contener letras y números.');
      //       }
      //     }

      //     // Validar monto inicial
      //     const initialAmountElement = document.getElementById('id_bank_initial_amount');
      //     if (initialAmountElement) {
      //       const amount = initialAmountElement.value;
      //       if (amount === '' || isNaN(parseFloat(amount))) {
      //         isValid = false;
      //         errorMessages.push('El monto inicial debe ser un número válido.');
      //       }
      //       else if (parseFloat(amount) < 0) {
      //         isValid = false;
      //         errorMessages.push('El monto inicial no puede ser negativo.');
      //       }
      //     }
      //   } else if (bankType === 'creditcard') {
      //     // Validar tarjeta de crédito
      //     const creditCardElement = document.getElementById('id_bank_credit_card');
      //     if (creditCardElement) {
      //       const creditCard = creditCardElement.value.replace(/\D/g, ''); // Eliminar no-dígitos
      //       if (!creditCard) {
      //         isValid = false;
      //         errorMessages.push('El número de tarjeta de crédito es obligatorio.');
      //       } else if (creditCard.length < 13 || creditCard.length > 16) {
      //         isValid = false;
      //         errorMessages.push('El número de tarjeta debe tener entre 13 y 16 dígitos.');
      //       }
      //     }
      //   }

      //   // Si hay errores de validación, mostrar SweetAlert y prevenir el envío
      //   if (!isValid) {
      //     event.preventDefault();

      //     let errorMessage = '<ul class="text-start">';
      //     errorMessages.forEach(msg => {
      //       errorMessage += `<li>${msg}</li>`;
      //     });
      //     errorMessage += '</ul>';

      //     Swal.fire({
      //       title: 'Error de validación',
      //       html: errorMessage,
      //       icon: 'error',
      //       confirmButtonText: 'Entendido'
      //     });
      //   } else {
      //     // Mostrar mensaje de procesando
      //     Swal.fire({
      //       title: 'Procesando...',
      //       text: 'Guardando la información bancaria',
      //       allowOutsideClick: false,
      //       allowEscapeKey: false,
      //       allowEnterKey: false,
      //       didOpen: () => {
      //         Swal.showLoading();
      //       }
      //     });
      //   }
      // });
    });
  </script>
{% endblock javascripts %}
