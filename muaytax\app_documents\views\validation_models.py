
import json
from io import Bytes<PERSON>
from openpyxl import load_workbook
from datetime import datetime, date

from django.http import HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import connection
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views import View

from muaytax.users.permissions import IsManagerRolePermission
from muaytax.app_sellers.models.seller import Seller

from muaytax.utils.general import get_period_details
from muaytax.utils.calc_models import SQL_model_dict, exec_sql, exec_sql_secure, prev_cas_model_130, prev_cas_model_303
from muaytax.app_documents.utils.validation_models import *


class ValidationModelSendRevision(LoginRequiredMixin, IsManagerRolePermission, View):

    def post(self, request, *args, **kwargs):
    
        seller = get_object_or_404(Seller, shortname=kwargs.get('shortname'))
        data = json.loads(request.body)
        model_id = data.get('model')
        year = data.get('year')
        period = data.get('period')
        prevData = data.get('prevData')
        response = {}
        error_messages = []
        
        first_month, latest_month, period = get_period_details(period)
        print("ChECK MODELS")

        # Obtain the data readjustment for the model 390
        if model_id == '390' and prevData:
            response = get_prev_data_model390(seller, year, first_month, latest_month)

        # Check data models before sending to revision
        elif not prevData:
            model_type_validations(seller, model_id, year, first_month, latest_month, period, error_messages)

        if error_messages:
            return JsonResponse(error_messages, status=400, safe=False)
        return JsonResponse(response, status=200)


class DownloadExcelCalcModel(LoginRequiredMixin, IsManagerRolePermission, View):
    """
    Descargar el archivo Excel de cálculo desglosado en SQL de los modelos
    """

    def get(self, request, *args, **kwargs):
        
        first_month, latest_month, periodo = get_period_details(request.GET.get('period'))
        seller = get_object_or_404(Seller, shortname=kwargs.get('shortname'))

        data = {
            'model': request.GET.get('model_id'),
            'year': request.GET.get('year'),
            'period': request.GET.get('period'),
            'seller': seller.pk,
            'first_month': first_month,
            'latest_month': latest_month,
            'periodo': periodo,
        }
        
        if data.get('model') == '130':
            prev_ca07, prev_ca19 = prev_cas_model_130(seller, data.get('year'), periodo)
            data.update({
                'prev_ca07': prev_ca07,
                'prev_ca19': prev_ca19,
            })
        elif data.get('model') == '303':
            campo_87_anterior, campo_71_anterior = prev_cas_model_303(seller, data.get('year'), periodo)
            data.update({
                'campo_87_anterior': campo_87_anterior,
                'campo_71_anterior': campo_71_anterior,
            })

        json_response = SQL_model_dict(data) # Obtiene el SQL del modelo
        if json_response is None or json_response == "" or json_response.get('sql') is None or json_response.get('params') is None:
            return HttpResponse("Invalid model", status=400)

        sql: str = json_response["sql"]
        params: tuple = json_response["params"]        
        json_result = exec_sql_secure(sql, params)

        template_path = f"/app/muaytax/static/assets/excel/{data.get('model')}_model_template_calcs.xlsx"

        if (json_result != None and json_result != ""):
            book = load_workbook(template_path)
            sheet = book.active  
            # Diccionario con FILA y COLUMNA donde escribir, mapeado a la clave del JSON
            positions = set_positions_excel(data.get('model'), json_result )

            # Escribir valores en las posiciones definidas
            for (fila, columna), valor in positions.items():
                sheet.cell(row=fila, column=columna, value=valor)

            output = BytesIO()
            book.save(output)
            output.seek(0)

            filename = f"Modelo_{data.get('model')}_{data.get('period')}_{data.get('year')}_Excel_{seller.shortname}.xlsx"

            response = HttpResponse(
                output.getvalue(),
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f'attachment; filename="{filename}"'
            return response

        return HttpResponse("No data available", status=400)
        

