DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'func_sellervat_countrycz_list_json') THEN
        DROP FUNCTION func_sellervat_countrycz_list_json(date_year INTEGER, date_period VARCHAR);
    END IF;
END $$;

CREATE OR REPLACE FUNCTION func_sellervat_countrycz_list_json(date_year INTEGER, date_period VARCHAR)
RETURNS jsonb AS $$
DECLARE
    inv_data RECORD;
    first_month DATE;
    last_month DATE;
    result_json jsonb := '[]';
BEGIN
    IF date_period = 'Q1' THEN
        first_month := date_year || '-01-01';
        last_month := date_year || '-04-01';
    ELSIF date_period = 'Q2' THEN
        first_month := date_year || '-04-01';
        last_month := date_year || '-07-01';
    ELSIF date_period = 'Q3' THEN
        first_month := date_year || '-07-01';
        last_month := date_year || '-10-01';
    ELSIF date_period = 'Q4' THEN
        first_month := date_year || '-10-01';
        last_month := (date_year + 1) || '-01-01';
    END IF;

    FOR inv_data IN
        SELECT DISTINCT
            subselect.*
        FROM (
            SELECT DISTINCT
                sel.id,
                sel.name AS seller_name,
                sel.shortname AS seller_shortname,
                (SELECT email FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS email,
                (SELECT name FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS user_name,
                (SELECT last_login FROM users_user usr WHERE usr.id = sel.user_id LIMIT 1) AS last_login,
                COUNT(DISTINCT inv.id) AS num_invoices,
                COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN inv.id ELSE NULL END) AS num_pending_invoices,
                COUNT(DISTINCT CASE WHEN inv.accounting_date >= first_month AND inv.accounting_date < last_month OR (inv.accounting_date IS NULL AND NOT inv.status_id = 'discard') THEN inv.id END) as num_invoices_this_period,
                ROUND(COALESCE(
                    100.0 * COUNT(DISTINCT CASE WHEN inv.status_id IN ('pending', 'revision-pending') THEN 1 ELSE NULL END) / NULLIF(COUNT(DISTINCT inv.id), 0),
                    0
                ), 2) AS percentage_pending_invoices
            FROM sellers_seller sel
            LEFT JOIN invoices_invoice inv ON (
                sel.id = inv.seller_id
                AND (inv.invoice_category_id NOT LIKE '%_copy' OR inv.invoice_category_id IS NULL)
            )
            LEFT JOIN sellers_sellervat sv ON sel.id = sv.seller_id AND sv.vat_country_id = 'CZ'
            LEFT JOIN (
                SELECT
                    sel.id as seller_id,
                    COUNT(DISTINCT CASE WHEN (i.accounting_date >= first_month AND i.accounting_date < last_month) OR (i.accounting_date IS NULL AND NOT i.status_id = 'discard') THEN i.id END) as num_inv,
                    COUNT(DISTINCT CASE WHEN  (i.accounting_date IS NULL AND i.status_id = 'revised' AND (i.transaction_type_id IS NULL OR i.transaction_type_id NOT IN ('outgoing-transfer', 'inbound-transfer'))) THEN i.id END) as num_inv_null_revised
                FROM sellers_seller sel
                LEFT JOIN invoices_invoice i ON sel.id = i.seller_id
                WHERE (i.invoice_category_id NOT LIKE '%_copy' OR i.invoice_category_id IS NULL)
                    AND i.tax_country_id = 'CZ'
                GROUP BY sel.id
            ) AS i ON sel.id = i.seller_id
            WHERE
                (
                    (sv.is_contracted = TRUE AND sv.vat_country_id = 'CZ' AND sv.activation_date < last_month)
                    OR (sv.is_contracted = FALSE AND sv.vat_country_id = 'CZ' AND i.num_inv > 0)
                    OR (i.num_inv > 0 AND NOT EXISTS (
                        SELECT 1 FROM sellers_sellervat sv2
                        WHERE sv2.vat_country_id = 'CZ'
                        AND sv2.seller_id = sel.id
                    ))
                )
                AND (sv.vat_country_id = 'CZ' AND (sv.deactivation_date IS NULL OR sv.deactivation_date >= first_month))
            GROUP BY sel.id
            ORDER BY sel.id
        ) AS subselect
    LOOP
        result_json := result_json || jsonb_build_object(
            'seller_id', inv_data.id,
            'seller_name', inv_data.seller_name,
            'shortname', inv_data.seller_shortname,
            'email', inv_data.email,
            'user_name', inv_data.user_name,
            'last_login', to_char(inv_data.last_login, 'YYYY-MM-DD HH24:MI:SS'),
            'num_pending_invoices', inv_data.num_pending_invoices,
            'percentage_pending_invoices', inv_data.percentage_pending_invoices,
            'num_invoices', inv_data.num_invoices_this_period
        );
    END LOOP;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql; 