from django import forms

from django.conf import settings
from django.utils.html import format_html
from django.db.utils import ProgrammingError, OperationalError

from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller import Seller
from muaytax.dictionaries.models.countries import Country
from muaytax.dictionaries.models.product_service import ProductService

from muaytax.app_documents.models.document import Document
from muaytax.app_partners.models.partner import Partner
from muaytax.app_address.models.address import Address
from muaytax.app_sellers.models.previous_accounting_manager import PreviousAccountingManager

debug = settings.DEBUG 

# Formulario para añadir direcciones
class AddressForm(forms.ModelForm):
    """Formulario para manejar la dirección dentro del socio."""

    class Meta:
        model = Address
        fields = [
            "address",
            "address_zip",
            "address_city",
            "address_state",
            "address_country"
        ]
        labels = {
            "address": "Dirección",
            "address_zip": "Código Postal",
            "address_city": "Ciudad",
            "address_state": "Estado / Provincia / Región",
            "address_country": "País",
        }
        widgets = {
            "address": forms.TextInput(attrs={"class": "form-control", "id": "id_address", "name": "address", "required": "required"}),
            "address_zip": forms.TextInput(attrs={"class": "form-control", "id": "id_address_zip", "name": "address_zip", "required": "required"}),
            "address_city": forms.TextInput(attrs={"class": "form-control", "id": "id_address_city", "name": "address_city", "required": "required"}),
            "address_state": forms.TextInput(attrs={"class": "form-control", "id": "id_address_state", "name": "address_state", "required": "required"}),
            "address_country": forms.Select(attrs={"class": "form-control select2", "id": "id_address_country", "name": "address_country", "required": "required"}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Definir placeholders para cada campo
        placeholders = {
            "address": "Dirección",
            "address_zip": "Código Postal",
            "address_city": "Ciudad",
            "address_state": "Estado / Provincia / Región",
            "address_country": "Seleccione un país"
        }

        for field_name, placeholder in placeholders.items():
            self.fields[field_name].widget.attrs["placeholder"] = placeholder

        # Asegurar que ciertos campos sean requeridos explícitamente
        required_fields = ["address", "address_zip", "address_city", "address_state", "address_country"]
        for field in required_fields:
            self.fields[field].required = True


        # Cargar opciones de países con manejo de errores durante makemigrations
        try:
            self.fields["address_country"].choices = [("", "-----")] + [
                (c.iso_code, c.name) for c in Country.objects.all()
            ]
        except (ProgrammingError, OperationalError, Exception) as e:
            # Solo para desarrollo / migración, evita romper el proceso
            self.fields["address_country"].choices = [("", "-----")]
        
## Formulario principal - Servicios IVA (mantenimiento o alta + mantenimiento)
class SellerVatForm(forms.ModelForm):
    """
    Formulario tipo MigrationInfoForm:
    - Carga maintenance_type como campo adicional y readonly.
    - Permite editar vat_number y contracting_date.
    """

    maintenance_type = forms.ChoiceField(
        required=False,
        choices=[
            ("", "----"),
            ("maintenance_subcription", "Alta nueva con mantenimiento"),
            ("maintenance", "Mantenimiento"),
        ],
        widget=forms.Select(attrs={
            "class": "form-control",
            "disabled": "disabled",
            "readonly": "readonly",
        }),
        label="Tipo de mantenimiento contratado"
    )

    class Meta:
        model = SellerVat
        fields = ["contracting_date"]  # Solo los editables
        labels = {
            "contracting_date": "Fecha de contratación",
        }
        widgets = {
            "contracting_date": forms.DateInput(attrs={
                "type": "date",
                "class": "form-control",
                "disabled": "disabled",
                "readonly": "readonly",
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get("instance")

        if instance:
            if instance.maintenance_type:
                self.fields["maintenance_type"].initial = instance.maintenance_type.code if hasattr(instance.maintenance_type, "code") else instance.maintenance_type

            if instance.contracting_date:
                self.fields["contracting_date"].initial = instance.contracting_date

## Includes de formularios - Servicios IVA (mantenimiento o alta + mantenimiento)
class CompanyInfoForm(forms.ModelForm):
    """
    Formulario para capturar la información general de la empresa.
    """
    
    company_address = AddressForm() # Formulario anidado para la dirección

    # Campo visible en frontend (solo número, sin prefijo)
    phone_national = forms.CharField(
        label="",
        required=True,
        widget=forms.TextInput(attrs={
            "class": "form-control",
            "id": "id_phone_national",
            "name": "phone_national",
            "placeholder": "Introduce el número sin prefijo",
        })
    )

    # Documentos
    business_registry = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "required": "required"
        }),
        label="",
    )

    business_deeds = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "required": "required"
        }),
        label="",
    )

    amazon_vies_screenshot = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "required": "required"
        }),
        label="Captura de pantalla Amazon (con la dirección para el VIES)*",
    )

    comparison_certificate = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "required": "required"
        }),
        label="",
    )

    mercantile_registry = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "required": "required"
        }),
        label="",
        help_text=format_html(
            'Si no lo tienes, puedes descargarlo: '
            '<a href="https://www.registradores.org/el-colegio/registro-mercantil" target="_blank" '
            'style="color: #003366; font-weight: bold;">aquí</a>'
        )
    )

    activity_type = forms.ChoiceField(
        required=False,
        choices=[
            ("", "----"),
            ('products', 'Productos'),
            ('services', 'Servicios'),
        ],
        widget=forms.Select(attrs={
            "class": "form-control",
            "required": "required",
            "id": "id_activity_type",
        }),
        label=""
    )

    class Meta:
        model = Seller
        fields = [
            "name", 
            "legal_entity",
            "phone_country", # instancia del prefijo telefónico
            "contact_phone", # campo real donde se guarda el prefijo + número
            "products_and_services",
            "desc_main_activity", 
        ]
        labels = {
            "name": "",
            "legal_entity": "",
            "phone_country": "",
            "contact_phone": "",
            "activity_type": "", 
            "products_and_services": "",
            "desc_main_activity": "",
        }
        widgets = {
            "name": forms.TextInput(attrs={
                "class": "form-control",
                "required": "required"
            }),
            "legal_entity": forms.TextInput(attrs={
                "class": "form-control",
                "required": "required"
            }),
            "contact_phone": forms.HiddenInput(),  # no se muestra directamente
            "phone_country": forms.Select(attrs={
                "class": "form-control",
                "id": "id_phone_country",
                "required": "required",
            }),
            "products_and_services": forms.Select(attrs={
                "class": "form-control",
                "id": "id_products_and_services",
                "disabled": "disabled",  # se habilita por JS
                "required": "required"
            }),
            "desc_main_activity": forms.Textarea(attrs={
                "class": "form-control",
                "required": "required",
                "maxlength": "50",
                "rows": 2,
                "placeholder": "Escribe máximo 50 caracteres"
            }),
        }

    def __init__(self, *args, **kwargs):
        self.seller = kwargs.pop("seller", None)
        activity_type = kwargs.pop("activity_type", None)
        initial_data = kwargs.get("initial", {})
        print("📦 Datos iniciales recibidos en CompanyInfoForm (initial_data):")
        for key, value in initial_data.items():
            if isinstance(value, dict):
                print(f"  🔸 {key}:")
                for subkey, subval in value.items():
                    print(f"    └── {subkey}: {subval}")
            else:
                print(f"  🔹 {key}: {value}")

        super().__init__(*args, **kwargs)

        # ==== PRODUCTOS / SERVICIOS ====
        if activity_type:
            self.fields["products_and_services"].queryset = ProductService.objects.filter(
                product_service_type=activity_type
            )
            self.initial["products_and_services"] = initial_data.get("products_and_services", "")
        else:
            self.fields["products_and_services"].queryset = ProductService.objects.none()

        # ==== DIRECCIÓN ====
        # Subformulario para dirección
        address_initial = initial_data.get("company_address", {})
        self.company_address = AddressForm(initial=address_initial, prefix="company_address")

        # Prefijo único para los IDs de los inputs
        field_id_prefix = "company_address"

        # Personalizar labels y placeholders + forzar IDs únicos
        address_fields_config = {
            "address": "Calle, número, piso...",
            "address_city": "Ej. Madrid",
            "address_state": "Ej. Comunidad de Madrid",
            "address_zip": "Ej. 28001",
            "address_country": "Selecciona un país",
        }

        for field_name, placeholder in address_fields_config.items():
            field = self.company_address.fields[field_name]
            field.label = ""
            field.widget.attrs["placeholder"] = placeholder
            field.widget.attrs["id"] = f"id_{field_id_prefix}_{field_name}"

        # ==== PREFIJO: usar address_country si no hay phone_country ====
        if not initial_data.get("phone_country"):
            address_country_iso = address_initial.get("address_country", "").upper().strip()
            if address_country_iso:
                self.initial["phone_country"] = address_country_iso
                print(f"📍 phone_country no estaba definido. Se usa address_country: {address_country_iso}")

        # ==== OPCIONES PARA PREFIJOS ====
        country_choices = [("", "-----")] + [
            (country.iso_code, f"{country.name} ( {country.phoneprefix} )")
            for country in Country.objects.exclude(phoneprefix__isnull=True).exclude(phoneprefix="").order_by("name")
        ]
        self.fields["phone_country"].choices = country_choices
        self.fields["phone_country"].label = ""

        # ==== phone_national (extraído de contact_phone si hay phone_country) ====
        iso_code = self.initial.get("phone_country") or initial_data.get("phone_country", "")
        phone_number = str(initial_data.get("contact_phone", "")).strip()

        if iso_code and phone_number:
            try:
                country_instance = Country.objects.get(iso_code=iso_code)
                prefix = country_instance.phoneprefix or ""
                if prefix and phone_number.startswith(prefix):
                    self.initial["phone_national"] = phone_number[len(prefix):].strip()
                    print(f"[INIT] phone_national inicializado desde contact_phone con prefijo {prefix}")
                else:
                    print(f"[INIT] ⚠️ {phone_number} no empieza con el prefijo esperado {prefix}")
            except Country.DoesNotExist:
                print(f"[INIT] ❌ País con ISO '{iso_code}' no encontrado")

    def clean(self):
        """Validaciones personalizadas del formulario."""
        cleaned_data = super().clean()

        # Validar que si la empresa es SL, debe tener un Registro Mercantil
        if cleaned_data.get("legal_entity") == "sl" and not self.cleaned_data.get("mercantile_registry"):
            self.add_error("mercantile_registry", "Debe proporcionar el Registro Mercantil para una SL.")

        return cleaned_data

class MigrationInfoForm(forms.ModelForm):
    """
    Formulario para capturar información de migración de un SellerVat.
    """

    previous_manager_address = AddressForm()  # Formulario anidado para la dirección del gestor

    vat_frequency = forms.ChoiceField(
        required=True,
        choices=[("mensual", "Mensual"), ("trimestral", "Trimestral"), ("anual", "Anual")],
        widget=forms.Select(attrs={
            "class": "form-control",
            "id": "id_vat_frequency",
            "name": "vat_frequency",
            "required": "required"
        })
    )

    france_old_manager_letter = forms.FileField(
        required=True,
        widget=forms.ClearableFileInput(attrs={
            "class": "form-control",
            "id": "id_france_old_manager_letter",
            "name": "france_old_manager_letter",
            "required": "required"
        }),
        label=""
    )

    class Meta:
        model = PreviousAccountingManager
        fields = [
            "previous_manager_end_date",  
            "previous_accounting_filed",
            "current_accounting_filed_date",
            "previous_manager_name",
        ]
        labels = {
            "previous_manager_end_date": "",
            "previous_accounting_filed": "",
            "current_accounting_filed_date": "",
            "previous_manager_name": "",
        }
        widgets = {
            "previous_manager_end_date": forms.DateInput(attrs={
                "type": "date",
                "class": "form-control",
                "required": "required"
            }),
            "previous_accounting_filed": forms.Select(choices=[
                ("", "----"), 
                ("True", "Sí"), 
                ("False", "No")
            ], attrs={
                "class": "form-control",
                "required": "required"
            }),
            "current_accounting_filed_date": forms.DateInput(attrs={
                "type": "date",
                "class": "form-control",
                "required": "required"
            }),
            "previous_manager_name": forms.TextInput(attrs={
                "class": "form-control",
                "required": "required"
            }),
        }

    def __init__(self, *args, **kwargs):
        """
        Se espera `seller_vat`, pero se elimina de `kwargs`
        antes de llamar a `super().__init__()`, para evitar errores.
        """
        seller_vat = kwargs.pop("seller_vat", None)
        super().__init__(*args, **kwargs)


    def clean(self):
        """Validación personalizada del formulario."""
        cleaned_data = super().clean()

        # Validar que si se elige "FR" se suba la carta**
        if "FR" in self.data.get("country", "") and not cleaned_data.get("france_old_manager_letter"):
            self.add_error("france_old_manager_letter", "Debe adjuntar la carta de desvinculación para Francia.")

        return cleaned_data

class PartnerForm(forms.ModelForm):
    """Formulario para agregar socios con dirección y documentos."""

    address = AddressForm()
    

    dni_file = forms.FileField(
        label="Adjuntar DNI",
        required=True,
        widget=forms.ClearableFileInput(attrs={"class": "form-control"})
    )

    passport_file = forms.FileField(
        label="Adjuntar Pasaporte",
        required=True,
        widget=forms.ClearableFileInput(attrs={"class": "form-control"})
    )

    class Meta:
        model = Partner
        fields = [
            "name",
            "last_name",
            "shares_percentage",
            "id_number", 
            "id_passport", 
            "start_date",  # Incluido en fields, pero NO definido aparte
            "end_date",  # Incluido en fields, pero NO definido aparte
        ]
        labels = {
            "id_number": "Número de Identificación (DNI/NIF)*",
            "id_passport": "Número de Pasaporte*",
            "start_date": "Fecha de Alta*",
            "end_date": "Fecha de Baja",
        }
        widgets = {
            "name": forms.TextInput(attrs={"class": "form-control", "id": "id_name", "name": "name", "required": "required"}),
            "last_name": forms.TextInput(attrs={"class": "form-control", "id": "id_last_name", "name": "last_name", "required": "required"}),
            "shares_percentage": forms.NumberInput(attrs={
                "class": "form-control",
                "id": "id_shares_percentage",
                "name": "shares_percentage",
                "value": "1.00",
                "min": 1,
                "max": 100,
                "step": "0.01",
                "required": "required",
                "placeholder": "solo el numero del 1 al 100"  # Agregado el placeholder
            }),
            "id_number": forms.TextInput(attrs={
                "class": "form-control",
                "required": "required",
            }),
            "id_passport": forms.TextInput(attrs={
                "class": "form-control",
                "required": "required",
            }), 
            "start_date": forms.DateInput(attrs={
                "type": "date",
                "class": "form-control",
                "id": "id_start_date",  # Asegura que coincida con el input en el HTML
                "name": "start_date",
                "required": "required",
            }),
            "end_date": forms.DateInput(attrs={
                "type": "date",
                "class": "form-control",
                "id": "id_end_date",  # Asegura que coincida con el input en el HTML
                "name": "end_date",
            }),
        }

class CountryDocumentForm(forms.ModelForm):
    DOCUMENTS_BY_ENTITY = {
        "self-employed": ["DE_VATSTATUS"],
        "llc": ["DE_FISCALRESIDENCE"],
        "sl": ["DE_FISCALIDCARD"],
        "other": [],
    }

    def __init__(self, *args, **kwargs):
        self.seller_vat = kwargs.pop("seller_vat", None)
        super().__init__(*args, **kwargs)

        if not self.seller_vat:
            return

        country_iso = self.seller_vat.vat_country.iso_code
        entity_type = self.seller_vat.seller.legal_entity

        allowed_fields = []

        # Documento obligatorio para todos los países
        if country_iso == "DE":
            # Dos documentos obligatorios para DE
            allowed_fields += [f"{country_iso}_CURRENTVATDOCS_1", f"{country_iso}_CURRENTVATDOCS_2"]
        else:
            allowed_fields.append(f"{country_iso}_CURRENTVATDOCS")

        # Documentos adicionales por país
        if country_iso == "DE":
            allowed_fields += ["DE_BANKCERTIFICATE", "DE_MERCHANTTOKEN"]
            allowed_fields += self.DOCUMENTS_BY_ENTITY.get(entity_type, [])
        elif country_iso == "GB":
            allowed_fields += ["GB_PROOFOFADDRESS_1", "GB_PROOFOFADDRESS_2"]
        elif country_iso == "AE":
            allowed_fields += [
                "AE_SALESINVOICES_1", "AE_SALESINVOICES_2", "AE_SALESINVOICES_3",
                "AE_SALESINVOICES_4", "AE_SALESINVOICES_5"
            ]

        debug and print(f"\nPaís: {country_iso}, Entidad: {entity_type}")
        debug and print(f"Campos permitidos: {allowed_fields}")

        self.fields.clear()
        for field_name in allowed_fields:
            self.fields[field_name] = forms.FileField(
                required=False,
                widget=forms.ClearableFileInput(attrs={"class": "form-control", "required": "required"}),
                label=""
            )

    class Meta:
        model = Document
        fields = []

