import time
import math
import json
import tempfile
import os
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, date, timedelta

from django.db import connection
from django.http import FileResponse, HttpResponse, JsonResponse
from django.db.models import Q, F, Sum, When, Value, DecimalField
from django.db.models.functions import Round, Coalesce

from muaytax.dictionaries.models import Country
from muaytax.dictionaries.models.vat_rates import VatRates
from muaytax.dictionaries.models import EconomicActivity
from muaytax.app_sellers.models import SellerVat, SellerYieldRecord, SellerVatActivity
from muaytax.app_providers.models import Provider
from muaytax.app_customers.models import Customer
from muaytax.app_invoices.models import Invoice, Concept
from muaytax.app_documents.models import PresentedModel, PresentedM54721120, AccountingRecord, ModelFormBE15
from muaytax.app_sellers.models.seller_184 import PresentedM184
from muaytax.app_representatives.models.representative import Representative
from muaytax.app_address.models.address import Address
from muaytax.users.models import User
from muaytax.app_sellers.utils import get_providers_model347, get_customers_model347
from muaytax.utils.reportlab import create_5472_pdf, add_signature_to_pdf
from muaytax.utils.api_currency import CurrencyConverter


from fillpdf import fillpdfs
from pypdf import PdfReader, PdfWriter

def SQL_model_dict (data):
    """Genera la consulta SQL para el modelo especificado."""

    seller = data.get('seller')
    model = data.get('model')
    year = data.get('year')
    first_month = data.get('first_month')
    latest_month = data.get('latest_month')

    model_dict = {
        "130": "SELECT func_calc_model_es_130(%s, %s, %s, %s, %s);",
        "303": "SELECT func_calc_model_es_303(%s, %s, %s, %s, %s, %s);",
        "309": "SELECT func_calc_model_es_309(%s, %s, %s, %s);",
    }

    sql = None
    if model in model_dict:
        sql = model_dict.get(model)

    params = None
    if model == "130":
        prev_ca07 = data.get('prev_ca07', 0)
        prev_ca19 = data.get('prev_ca19', 0)
        params = (seller, year, latest_month, prev_ca07, prev_ca19)
    elif model == "303":
        campo_71_anterior = data.get('campo_71_anterior', 0)
        campo_87_anterior = data.get('campo_87_anterior', 0)
        params = (seller, year, first_month, latest_month, campo_71_anterior, campo_87_anterior)
    elif model == "309":
        params = (seller, year, first_month, latest_month)
    else:
        params = None

    return {'sql': sql, 'params': params}

def prev_cas_model_130(seller, year, periodo)-> tuple[float, float]:
    """Obtiene las casillas 7 y 19 del modelo 130 de trimestres anteriores."""

    # Obtener PM Anteriores (Mismo Año)
    periodos_anteriores = []
    for i in range(1, int(str(periodo).replace("Q", "").replace("T", ""))):
        periodos_anteriores.append(f"Q{i}")

    past_models = PresentedModel.objects.filter(seller=seller, model='ES-130', status='presented', year=year,
                                                period__in=periodos_anteriores)
    prev_ca07 = 0
    prev_ca19 = 0
    # Obtener Casillas de Trimestres anteriores
    try:
        for past_model in past_models:
            json_pdf = past_model.json_pdf
            if (json_pdf != None):
                json_pdf_obj = json.loads(json_pdf)
                if json_pdf_obj is not None and 'campo_07' in json_pdf_obj:
                    # Casilla 7 de Trimestres Anteriores
                    casilla_07 = str(json_pdf_obj['campo_07']).replace('.', '').replace(',', '.').replace(" ", "")
                    casilla_number_07 = float(casilla_07) if casilla_07 != "" else 0
                    casilla_number_07 = casilla_number_07 if casilla_number_07 > 0 else 0
                    prev_ca07 += float(casilla_number_07)
                    # Casilla 19 de Trimestres Anteriores
                    casilla_19 = str(json_pdf_obj['campo_19']).replace('.', '').replace(',', '.').replace(" ", "")
                    casilla_number_19 = float(casilla_19) if casilla_19 != "" else 0
                    casilla_number_19 = casilla_number_19 * -1 if casilla_number_19 < 0 else 0
                    prev_ca19 += float(casilla_number_19)
    except:
        print("Error al obtener casillas de trimestres anteriores")

    return prev_ca07, prev_ca19

def prev_cas_model_303(seller, year, periodo)-> tuple[float, float]:
    """Obtiene las casillas 87 y 71 del modelo 303 de trimestres anteriores."""
    
    # Recuperamos los datos del periodo anterior
    campo_87_anterior = 0
    campo_71_anterior = 0
    try:
        last_period = int(periodo.replace("T", "")) - 1
        last_year = int(year)
        if last_period <= 0:
            last_year -= 1
            last_year = str(last_year)
            last_period = "Q4"
        else:
            last_year = str(last_year)
            last_period = f"Q{last_period}"
        print(f"Periodo Anterior: {last_period} - {last_year}")

        pm = PresentedModel.objects.filter(seller=seller, year=last_year, period__code=last_period, model='ES-303',
                                           status__code='presented', country__iso_code='ES').first()

        if pm is not None:
            if pm.json_pdf is not None:
                json_pdf_obj = json.loads(pm.json_pdf)
                if json_pdf_obj is not None:

                    # Recuperar Campo 87 Anterior
                    if pm.period.code == 'Q4' and pm.choice_seller == 'devolution':
                        campo_87_anterior = 0.0
                    else:
                        try:
                            # print(f"json_pdf_obj['campo_87']: {json_pdf_obj['campo_87']}")
                            campo_87_anterior = json_pdf_obj['campo_87'] if json_pdf_obj['campo_87'] else "0"
                            campo_87_anterior = float(campo_87_anterior.replace(".", "").replace(",", "."))
                            print(f"campo_87_anterior: {campo_87_anterior}")
                        except:
                            print("Error al recuperar campo 87 del trimestre anterior")
                            campo_87_anterior = 0.0

                    # Recuperar Campo 71 Anterior
                    if pm.period.code == 'Q4' and pm.choice_seller == 'devolution':
                        campo_71_anterior = 0.0
                    else:
                        try:
                            print(f"json_pdf_obj['campo_71']: {json_pdf_obj['campo_71']}")
                            campo_71_anterior = json_pdf_obj['campo_71'] if json_pdf_obj['campo_71'] else "0"
                            campo_71_anterior = float(campo_71_anterior.replace(".", "").replace(",", "."))
                            campo_71_anterior = campo_71_anterior * -1 if campo_71_anterior < 0 else 0.0
                            print(f"campo_71_anterior: {campo_71_anterior}")
                        except:
                            print("Error al recuperar campo 71 del trimestre anterior")
                            campo_71_anterior = 0.0
            else:
                print(f"No tiene informacion del trimestre anterior: El campo json_pdf es None: {pm.pk}")
        else:
            print(
                f"No tiene informacion del trimestre anterior: No se encontro el Modelo 303 del periodo {last_period} - {last_year}")
    except Exception as e:
        print(f"Error al recuperar campos de trimestre anterior: {e}")
    return campo_87_anterior, campo_71_anterior

def is_safe_param(param):
    import re
    """
    Devuelve True si el parámetro es seguro, False si es sospechoso.
    """
    if param is None:
        return True  # nada que validar

    if isinstance(param, (int, float)):
        return True  # valores numéricos son seguros

    # Convierte todo a string para evaluar
    param_str = str(param).lower()

    # Lista negra básica de patrones peligrosos
    blacklist_patterns = [
        r";",              # punto y coma (fin de instrucción SQL)
        r"--",             # comentario SQL
        r"/\*",            # inicio de comentario multilinea
        r"\*/",            # fin de comentario multilinea
        r"\b(drop|delete|update|insert|alter|truncate|exec)\b",
        r"union\b",
        r"select\b.*\bfrom\b",  # select típico
    ]

    return not any(re.search(pat, param_str) for pat in blacklist_patterns)

def exec_sql_secure(sql: str, params: tuple):
    # Validación antes de ejecutar
    for param in params:
        if not is_safe_param(param):
            raise ValueError(f"Parametro sospechoso detectado: {param}")
    return exec_sql(sql, params)

def exec_sql(sql: str, params: tuple = ()):
    sql_result = ""
    try:
        with connection.cursor() as cursor:
            try:
                if (params is None or len(params) == 0):
                    cursor.execute(sql) # EJECUCION INSEGURA
                else:
                    cursor.execute(sql, params) # EJECUCION SEGURA INCLUSO SI LE LLEGAN PARAMS MALICIOSOS
                row = cursor.fetchone()
                while row is not None:
                    sql_result += str(row) + "\n"
                    row = cursor.fetchone()
            except:
                print("Error al ejecutar SQL")
    except:
        print("Error al conectar con la base de datos")

    if (sql_result != None and sql_result != ""):
        sql_result = sql_result.replace("('", "[").replace("',)", "]").replace("}','{", "},{")
        json_result = json.loads(sql_result)
        json_result = json_result[0]

        return json_result

def calcModel349_table(seller, year, first_month, latest_month):

    
    # invoices_transfer = Invoice.objects.all().filter(seller_id=seller.id, status='revised').filter(
    #     accounting_date__year=year).filter(accounting_date__month__gte=first_month).filter(
    #     accounting_date__month__lte=latest_month).filter((Q(transaction_type='intra-community-expense') | Q(
    #     transaction_type='intra-community-sale') | Q(transaction_type='intra-community-refund') | Q(
    #     transaction_type='intra-community-credit') | Q(transaction_type='outgoing-transfer') | Q(
    #     transaction_type='inbound-transfer')) & Q(tax_country='ES')).exclude(is_generated_amz=True).exclude(customer__name__icontains= 'Clientes Particulares')
        
    # concept_transfer = Concept.objects.filter(invoice__in=invoices_transfer)

    # list_provider_ids = []
    # list_customer_ids = []

    # for x in invoices_transfer.values('provider').distinct():
    #     if (x['provider'] is not None):
    #         list_provider_ids.append(x['provider'])

    # providers = Provider.objects.filter(id__in=list_provider_ids)
    # total_operators_provider = providers.distinct().count()

    # for x in invoices_transfer.values('customer').distinct():
    #     if (x['customer'] is not None):
    #         list_customer_ids.append(x['customer'])

    # customers = Customer.objects.filter(id__in=list_customer_ids)
    # total_operators_customer = customers.distinct().count()

    # total_operators = total_operators_provider + total_operators_customer

    # total = 0

    # operadores_customer = []
    # operadores_provider = []

    # for operator in providers:
    #     if operator is not None:
    #         country = operator.country.iso_code if operator.country is not None else "-"
    #         name = operator.name
    #         nif = operator.nif_cif_iva[2:] if operator.nif_cif_iva is not None else None
    #         all_nif = operator.nif_cif_iva

    #         value_amz = concept_transfer.filter(invoice__provider=operator, invoice__is_txt_amz=True).aggregate(
    #             total=Sum('amount_euros'))['total'] or 0
    #         value_common = \
    #             concept_transfer.filter(invoice__provider=operator).exclude(invoice__is_txt_amz=True).aggregate(
    #                 total=Sum(F('amount_euros') * F('quantity')))['total'] or 0
    #         value = value_amz + value_common

    #         total += value

    #         if operator.account_expenses is not None and operator.account_expenses.code == '600':
    #             clave = 'A'
    #         else:
    #             clave = 'I'

    #         operadores_provider.append(
    #             {"country": country, "name": name, "nif": nif, "all_nif": all_nif, "value": value, "clave": clave})

    # for operator in customers:
    #     if operator is not None:
    #         country = operator.country.iso_code if operator.country is not None else "-"
    #         name = operator.name
    #         nif = operator.nif_cif_iva[2:] if operator.nif_cif_iva is not None else None
    #         all_nif = operator.nif_cif_iva

    #         value_amz = concept_transfer.filter(invoice__customer=operator, invoice__is_txt_amz=True).aggregate(
    #             total=Sum('amount_euros'))['total'] or 0
    #         value_common = \
    #             concept_transfer.filter(invoice__customer=operator).exclude(invoice__is_txt_amz=True).aggregate(
    #                 total=Sum(F('amount_euros') * F('quantity')))['total'] or 0
    #         value = value_amz + value_common

    #         total += value

    #         if operator.account_sales is not None and operator.account_sales.code == '700':
    #             clave = 'E'
    #         else:
    #             clave = 'S'

    #         operadores_customer.append(
    #             {"country": country, "name": name, "nif": nif, "all_nif": all_nif, "value": value, "clave": clave})

    # contenido_html = "<link rel=\"stylesheet\" crossorigin href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css\"  type=\"text/css\" />"

    # contenido_html += " <br>" + "Total de operadores intracomunitarios: " + str(total_operators) + " <br>"
    # contenido_html += "Importe de las operaciones intracomunitarias: " + str(round(total, 2)).replace('.',
    #                                                                                                   ',') + " <br>"
    # contenido_html += """
    #     <table class="table table-bordered">
    #     <thead class="thead-dark">
    #         <tr>
    #         <th>PAIS</th>
    #         <th>NIF</th>
    #         <th>NIF_COMPLETO</th>
    #         <th>NOMBRE</th>
    #         <th>IMPORTE</th>
    #         <th>CLAVEOP</th>
    #         </tr>
    #     </thead>
    #     <tbody>
    #     """

    # for operator in operadores_provider:
    #     if operator['value'] > 0:
    #         contenido_html += "<tr><td>" + str(operator['country']) + "</td><td>" + str(
    #             operator['nif']) + "</td><td>" + str(operator['all_nif']) + "</td><td>" + str(
    #             operator['name']) + "</td><td>" + str(round(operator['value'], 2)).replace('.',
    #                                                                                        ',') + "</td><td>" + str(
    #             operator['clave']) + "</td></tr>"

    # contenido_html += "<tr><td></td><td></td><td></td><td></td><td></td><td></td></tr>"

    # for operator in operadores_customer:
    #     if operator['value'] > 0:
    #         contenido_html += "<tr><td>" + str(operator['country']) + "</td><td>" + str(
    #             operator['nif']) + "</td><td>" + str(operator['all_nif']) + "</td><td>" + str(
    #             operator['name']) + "</td><td>" + str(round(operator['value'], 2)).replace('.',
    #                                                                                        ',') + "</td><td>" + str(
    #             operator['clave']) + "</td></tr>"

    contenido_html = "<br> <br> <p><b> Tabla Removida por quedarse en desuso. Chequear lo valores de la tabla del PDF que esta mas actualizado.</b></p> <br> <br>"

    response = HttpResponse(contenido_html)

    return response

def calcModel369_table(seller, year, periodo, first_month, latest_month):
    # # APARTADO 4
    # # Todas las OSS sales (menos las OSS refund) en el que el país de SALIDA sea ESPAÑA. Se agrupan por país de consumo (o sea, por tax country).
    # # Si para algúm País se usan tipos diferentes de IVA, hay que hacer una linea de ese mismo país por cada tipo de IVA

    # invoices = Invoice.objects.all().filter(seller=seller, status__code='revised').exclude(
    #     transaction_type__code__icontains='-transfer').filter(accounting_date__year=year).filter(
    #     accounting_date__month__gte=first_month).filter(accounting_date__month__lte=latest_month).exclude(
    #     is_generated_amz=True)
    # tax_country = invoices.filter(
    #     departure_country__iso_code="ES",
    #     status__code="revised",
    #     transaction_type__code__in=["oss", "oss-refund"],
    # ).values('tax_country').distinct()

    # aux = {}
    # for tc in tax_country:
    #     pais = tc['tax_country']
    #     aux[pais] = {}
    #     rates = VatRates.objects.filter(country_code=pais).values('vat_rates').distinct()
    #     if rates and len(rates) > 0:
    #         rates = rates[0]
    #         rates = rates['vat_rates'].replace(' ', '').split(",")
    #         for rate in rates:
    #             rate_rounded = float(rate)
    #             qs = Concept.objects.filter(
    #                 invoice__in=invoices,
    #                 invoice__departure_country__iso_code="ES",
    #                 invoice__status__code="revised",
    #                 invoice__transaction_type__code__in=["oss", "oss-refund"],
    #                 invoice__tax_country__iso_code=pais,
    #             ).annotate(rounded_vat=Round('vat', 2)).filter(rounded_vat=rate_rounded)

    #             base = 0
    #             cuota = 0
    #             amzbase = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros')))['total']
    #             amzcuota = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('vat') / 100))[
    #                 'total']
    #             commonbase = \
    #                 qs.exclude(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('quantity')))[
    #                     'total']
    #             commoncuota = qs.exclude(invoice__is_txt_amz=True).aggregate(
    #                 total=Sum(F('quantity') * F('amount_euros') * F('vat') / 100))['total']
    #             if amzbase and amzbase is not None:
    #                 base += amzbase
    #             if amzcuota and amzcuota is not None:
    #                 cuota += amzcuota
    #             if commonbase and commonbase is not None:
    #                 base += commonbase
    #             if commoncuota and commoncuota is not None:
    #                 cuota += commoncuota

    #             aux[pais][rate_rounded] = {'base': base, 'cuota': cuota}

    # # print("APARTADO 4:" + str(aux) + "\n\n")

    # contenido_html = "<link rel=\"stylesheet\" crossorigin href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css\"  type=\"text/css\" />"
    # contenido_html += "<h4>Entregas de bienes expedidos o transportados desde EMID España</h4>"

    # contenido_html += """
    #     <table class="table table-bordered">
    #     <thead class="thead-dark">
    #         <tr>
    #         <th>Código Pais</th>
    #         <th>Tipo IVA</th>
    #         <th>Base imponible</th>
    #         <th>Cuota IVA</th>
    #         </tr>
    #     </thead>
    #     <tbody>
    #     """
    # # Suma de la Casilla D del apartado 4 de las filas donde el TAX COUNTRY sea el mismo aunque el tipo IVA sea diferente
    # apartado8array = []
    # txt_json1 = []
    # type_rate1 = ''

    # for keypais, pais in aux.items():
    #     cuotaspais = 0
    #     for keyrate, rate in pais.items():
    #         if rate['base'] != 0 and rate['cuota'] != 0:
    #             cuotaspais += round(rate['cuota'], 2)

    #             vat_query = VatRates.objects.filter(country_code=keypais).first()
    #             if int(vat_query.vat_default) == int(keyrate):
    #                 type_rate1 = 'S'
    #             else:
    #                 type_rate1 = 'R'
    #             contenido_html += "<tr><td>" + str(keypais) + "</td><td>" + str(keyrate) + "</td><td>" + str(
    #                 round(rate['base'], 2)).replace('.', ',') + "</td><td>" + str(round(rate['cuota'], 2)).replace('.',
    #                                                                                                                ',') + "</td></tr>"
    #             txt_json1.append(
    #                 {'codPais': keypais, 'iva': "{:.2f}".format(keyrate).replace('.', ''), 'tipoIVA': type_rate1,
    #                  'base': "{:.2f}".format(round(rate['base'], 2)).replace('.', ''),
    #                  'cuota': "{:.2f}".format(round(rate['cuota'], 2)).replace('.', '')})

    #     apartado8array.append({'pais': keypais, 'cuota': cuotaspais, 'cuota2': 0})

    # contenido_html += "</tbody></table>"

    # # APARTADO 6
    # # Todas las OSS sales (menos las OSS refund) en el que el país de SALIDA NO sea ESPAÑA. Se agrupan por país de envío(o sea, por departure country).
    # # Si para algúm País se usan tipos diferentes de IVA, hay que hacer una linea de ese mismo país por cada tipo de IVA

    # departure_country = invoices.filter(
    #     status__code="revised",
    #     transaction_type__code__in=["oss", "oss-refund"],
    # ).exclude(departure_country__iso_code="ES").exclude(departure_country=None).values('departure_country').distinct()

    # auxd = {}
    # for dc in departure_country:
    #     paisd = dc['departure_country']
    #     auxd[paisd] = {}

    #     tax_country = invoices.filter(
    #         departure_country__iso_code=paisd,
    #         status__code="revised",
    #         transaction_type__code__in=["oss", "oss-refund"],
    #     ).values('tax_country').distinct()

    #     aux = {}
    #     for tc in tax_country:
    #         pais = tc['tax_country']
    #         aux[pais] = {}
    #         rates = VatRates.objects.filter(country_code=pais).values('vat_rates').distinct()
    #         if rates and len(rates) > 0:
    #             rates = rates[0]
    #             rates = rates['vat_rates'].replace(' ', '').split(",")
    #             for rate in rates:
    #                 rate_rounded = float(rate)
    #                 qs = Concept.objects.filter(
    #                     invoice__in=invoices,
    #                     invoice__tax_country__iso_code=pais,
    #                     invoice__departure_country__iso_code=paisd,
    #                     invoice__status__code="revised",
    #                     invoice__transaction_type__code__in=["oss", "oss-refund"],
    #                 ).annotate(rounded_vat=Round('vat', 2)).filter(rounded_vat=rate_rounded)
    #                 base = 0
    #                 cuota = 0
    #                 amzbase = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros')))['total']
    #                 amzcuota = \
    #                     qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('vat') / 100))[
    #                         'total']
    #                 commonbase = \
    #                     qs.exclude(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('quantity')))[
    #                         'total']
    #                 commoncuota = \
    #                     qs.exclude(invoice__is_txt_amz=True).aggregate(total=Sum(F('vat_euros') * F('quantity')))[
    #                         'total']
    #                 if amzbase and amzbase is not None:
    #                     base += amzbase
    #                 if amzcuota and amzcuota is not None:
    #                     cuota += amzcuota
    #                 if commonbase and commonbase is not None:
    #                     base += commonbase
    #                 if commoncuota and commoncuota is not None:
    #                     cuota += commoncuota

    #                 aux[pais][rate_rounded] = {'base': base, 'cuota': cuota}
    #                 auxd[paisd] = aux

    # # print("APARTADO 6:"+str(auxd)+ "\n\n")

    # contenido_html += "<br><link rel=\"stylesheet\" crossorigin href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css\"  type=\"text/css\" />"
    # contenido_html += "<h4>Entregas de bienes expedidos o transportados desde otros EM distintos de España</h4>"

    # contenido_html += """
    #     <table class="table table-bordered">
    #     <thead class="thead-dark">
    #         <tr>
    #         <th>Código Pais de envío</th>
    #         <th>NIVA /Otro código Identificación fiscal</th>
    #         <th>Código País EM de consumo</th>
    #         <th>Tipo IVA</th>
    #         <th>Base imponible</th>
    #         <th>Cuota IVA</th>
    #         </tr>
    #     </thead>
    #     <tbody>
    #     """
    # # Suma de la Casilla F del apartado 6 de las filas donde el TAX COUNTRY sea el mismo aunque el tipo IVA sea diferente
    # apartado8array2 = []
    # txt_json2 = []
    # type_rate2 = ''
    # otroCod = ''

    # for keypaisd, paisd in auxd.items():
    #     for keypais, pais in paisd.items():
    #         cuotaspais = 0
    #         for keyrate, rate in pais.items():
    #             if rate['base'] != 0 and rate['cuota'] != 0:
    #                 cif = ""
    #                 sellevats = SellerVat.objects.filter(seller__id=seller.id, vat_country__iso_code=keypaisd).values(
    #                     'vat_number')
    #                 if sellevats and len(sellevats) > 0:
    #                     sellevats = sellevats[0]
    #                     cif = sellevats['vat_number']
    #                 cuotaspais += round(rate['cuota'], 2)
    #                 vat_query = VatRates.objects.filter(country_code=keypais).first()
    #                 if int(vat_query.vat_default) == int(keyrate):
    #                     type_rate2 = 'S'
    #                 else:
    #                     type_rate2 = 'R'

    #                 if cif and len(cif) > 0:
    #                     otroCod = cif[2:]

    #                 contenido_html += "<tr><td>" + str(keypaisd) + "</td><td>" + str(cif) + "</td><td>" + str(
    #                     keypais) + "</td><td>" + str(keyrate) + "</td><td>" + str(round(rate['base'], 2)).replace('.',
    #                                                                                                               ',') + "</td><td>" + str(
    #                     round(rate['cuota'], 2)).replace('.', ',') + "</td></tr>"
    #                 txt_json2.append({'codPais_env': keypaisd, 'otroCod': otroCod, 'paisConsumo': keypais,
    #                                   'iva': "{:.2f}".format(keyrate).replace('.', ''),
    #                                   'base': "{:.2f}".format(round(rate['base'], 2)).replace('.', ''),
    #                                   'cuota': "{:.2f}".format(round(rate['cuota'], 2)).replace('.', ''),
    #                                   'tipoIVA': type_rate2})
    #         apartado8array2.append({'pais': keypais, 'cuota': 0, 'cuota2': cuotaspais})
    # contenido_html += "</tbody></table>"

    # # APARTADO 8
    # # Es una recapitulación, un total de todos los apartados, dividido por TAX COUNTRY y teniendo en cuenta todos los tipos de IVA

    # # A 	Sigla del TAX COUNTRY
    # # B 	-
    # # C 	Suma de la Casilla D del apartado 4 de las filas donde el TAX COUNTRY sea el mismo aunque el tipo IVA sea diferente
    # # D	Lo mismo que la casilla C
    # # E	-
    # # F	Suma de la Casilla F del apartado 6 de las filas donde el TAX COUNTRY sea el mismo aunque el tipo IVA sea diferente
    # # G	Lo mismo que la casilla F
    # # H	Suma de la casilla D y la casilla G
    # # I	-
    # # J	-
    # # K	Lo mismo que la casilla H
    # # L	Lo mismo que la casilla H
    # #
    # # contenido_html += "<br><link rel=\"stylesheet\" crossorigin href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css\"  type=\"text/css\" />"

    # # print("APARTADO 8 array1: "+str(apartado8array)+ "\n\n")
    # # print("APARTADO 8 array2: "+str(apartado8array2)+ "\n\n")

    # contenido_html += "<h4>Resultado de la autoliquidación por EM de consumo</h4>"
    # contenido_html += """
    #     <table class="table table-bordered">
    #     <thead class="thead-dark">
    #         <tr>
    #         <th>Código País EM de consumo</th>
    #         <th>Prestaciones de servicios</th>
    #         <th>Entregas de bienes</th>
    #         <th>Suma cuotas apdos. 3 y 4</th>
    #         <th>Prestaciones de servicios (Apdo. 5)</th>
    #         <th>Entregas de bienes (Suma cuota Apdo. 6)</th>
    #         <th>Suma cuotas Apdos. 5 y 6</th>
    #         <th>Total cuotas del periodo</th>
    #         <th>Correcciones positivas apdo. 7</th>
    #         <th>Correcciones positivas apdo. 7</th>
    #         <th>Resultado por EM de consumo</th>
    #         <th>A ingresar en España</th>
    #         <th>A devolver por el EM de consumo</th>
    #         </tr>
    #     </thead>
    #     <tbody>
    #     """
    # casillac = 0
    # casillaf = 0
    # casillah = 0
    # totalsuma = 0
    # arrayauxapart8 = []
    # sumaarrayauxapart8 = []

    # # if apartado8array2 and len(apartado8array2) > 0:
    # #     for pais in apartado8array:
    # #         for pais2 in apartado8array2:
    # #             if pais['pais'] == pais2['pais']:
    # #                     arrayauxapart8.append({'pais': pais['pais'], 'cuota': round(pais['cuota'],2), 'cuota2': round(pais2['cuota2'],2)})

    # #     for pais2 in apartado8array2:
    # #         if pais['pais'] != pais2['pais']:
    # #             arrayauxapart8.append({'pais': pais2['pais'], 'cuota': 0, 'cuota2': round(pais2['cuota2'],2)})

    # # else:
    # #     for pais in apartado8array:
    # #         arrayauxapart8.append({'pais': pais['pais'], 'cuota': round(pais['cuota'],2), 'cuota2': 0})

    # if apartado8array2 and len(apartado8array2) > 0:
    #     for pais in apartado8array:
    #         arrayauxapart8.append(
    #             {'pais': pais['pais'], 'cuota': round(pais['cuota'], 2), 'cuota2': round(pais['cuota2'], 2)})
    #     for pais2 in apartado8array2:
    #         arrayauxapart8.append(
    #             {'pais': pais2['pais'], 'cuota': round(pais2['cuota'], 2), 'cuota2': round(pais2['cuota2'], 2)})
    # else:
    #     for pais in apartado8array:
    #         arrayauxapart8.append({'pais': pais['pais'], 'cuota': round(pais['cuota'], 2), 'cuota2': 0})

    # arrayauxapart8 = sorted(arrayauxapart8, key=lambda x: x['pais'])
    # # print("APARTADO 8 arrayauxapart8: "+str(arrayauxapart8)+ "\n\n")
    # temp_dict = {}
    # for i in arrayauxapart8:
    #     if i['pais'] in temp_dict:
    #         temp_dict[i['pais']]['cuota'] += i['cuota']
    #         temp_dict[i['pais']]['cuota2'] += i['cuota2']
    #     else:
    #         temp_dict[i['pais']] = {'pais': i['pais'], 'cuota': i['cuota'], 'cuota2': i['cuota2']}
    # sumaarrayauxapart8 = [{key: value} for key, value in temp_dict.items()]

    # total_casillac = 0
    # total_casillaf = 0
    # total_casillah = 0

    # for pais in sumaarrayauxapart8:
    #     for paisCode in pais:
    #         # if pais[paisCode]['cuota'] != 0 and pais[paisCode]['cuota2'] != 0:
    #         casillac = round(pais[paisCode]['cuota'], 2)
    #         casillaf = round(pais[paisCode]['cuota2'], 2)
    #         casillah = round(casillac + casillaf, 2)
    #         total_casillac += round(casillac, 2)
    #         total_casillaf += round(casillaf, 2)
    #         total_casillah += round(casillah, 2)
    #         totalsuma += round(casillah, 2)
    #         contenido_html += "<tr><td>" + str(paisCode) + "</td><td>-</td><td>" + str(casillac).replace('.',
    #                                                                                                      ',') + "</td><td>" + str(
    #             casillac).replace('.', ',') + "</td><td>-</td><td>" + str(casillaf).replace('.',
    #                                                                                         ',') + "</td><td>" + str(
    #             casillaf).replace('.', ',') + "</td><td>" + str(casillah).replace('.',
    #                                                                               ',') + "</td><td>-</td><td>-</td><td>" + str(
    #             casillah).replace('.', ',') + "</td><td>" + str(casillah).replace('.', ',') + "</td><td>-</td></tr>"

    # total_casillac = round(total_casillac, 2)
    # total_casillaf = round(total_casillaf, 2)
    # total_casillah = round(total_casillah, 2)

    # contenido_html += "<tr><th>TOTAL</th><td>-</td><th>" + str(total_casillac).replace('.', ',') + "</th><th>" + str(
    #     total_casillac).replace('.', ',') + "</th><td>-</td><th>" + str(total_casillaf).replace('.',
    #                                                                                             ',') + "</th><th>" + str(
    #     total_casillaf).replace('.', ',') + "</th><th>" + str(total_casillah).replace('.',
    #                                                                                   ',') + "</th><td>-</td><td>-</td><th>" + str(
    #     total_casillah).replace('.', ',') + "</th><th>" + str(total_casillah).replace('.', ',') + "</th><td>-</td></tr>"
    # contenido_html += "</tbody></table>"
    # contenido_html += "<br>Suma Total cuotas del periodo: " + str(round(totalsuma, 2)) + "<br>"

    # # response = HttpResponse(contenido_html)

    # # return response

    # # genera txt pruebas

    # info_seller = []
    # year = str(year)
    # trimestre = str(periodo)
    # trimestre_numero = str(periodo).replace('Q', '').replace('T', '')
    # iva_espana = SellerVat.objects.filter(seller__id=seller.id, vat_country__iso_code='ES').values('vat_number')

    # if iva_espana and len(iva_espana) > 0:
    #     iva_espana = iva_espana[0]
    #     iva_espana = iva_espana['vat_number']
    # iva_sin_pais = str(iva_espana).replace('ES', '')
    # iva_con_pais = str(iva_espana)

    # if invoices:
    #     activity = '0'
    # else:
    #     activity = '1'

    # sellerformat = seller.name.upper().replace(',', '').replace('.', '')
    # info_seller.append(
    #     {'nif': iva_sin_pais, 'nif_pais': iva_con_pais, 'nombre_emp': sellerformat, 'activity': activity})

    contenido_html = "<br> <br> <p><b> Tabla Removida por quedarse en desuso. Chequear lo valores de la tabla del PDF que esta mas actualizado.</b></p> <br> <br>"
    response = HttpResponse(contenido_html)
    return response

def calcModel111DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration
    nombre = seller.name
    ejercicio = year

    # JSON Base
    jsonValues = {
        "nif": nif,
        "nombre": nombre,
        "ejercicio": ejercicio,
        "periodo": periodo,
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_111({seller.pk}, {year}, {first_month}, {latest_month});"
    json_result = exec_sql_secure(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        # JSON PDF
        jsonValues.update(
            {
                "casilla_01": str(json_result['CA01']).replace(".", ","),
                "casilla_02": str(json_result['CA02']).replace(".", ","),
                "casilla_03": str(json_result['CA03']).replace(".", ","),
                "casilla_04": str(json_result['CA04']).replace(".", ","),
                "casilla_05": str(json_result['CA05']).replace(".", ","),
                "casilla_06": str(json_result['CA06']).replace(".", ","),
                "casilla_07": str(json_result['CA07']).replace(".", ","),
                "casilla_08": str(json_result['CA08']).replace(".", ","),
                "casilla_09": str(json_result['CA09']).replace(".", ","),
                "casilla_28": str(json_result['CA28']).replace(".", ","),
                "casilla_30": str(json_result['CA30']).replace(".", ","),
            }
        )

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_111.pdf"

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500 ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel115DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration
    nombre = seller.name
    ejercicio = year

    # JSON Base
    jsonValues = {
        "page1_field6": nif,
        "page1_field7": nombre,
        "page1_field2": ejercicio,
        "page1_field3": periodo,
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_115({seller.pk}, {year}, {first_month}, {latest_month});"
    json_result = exec_sql_secure(sql)

    if (json_result != None and json_result != ""):

        # JSON PDF
        jsonValues.update(
            {
                "page1_field10": str(json_result['CA01']).replace(".", ","),
                "page1_field11": str(json_result['CA02']).replace(".", ","),
                "page1_field12": str(json_result['CA03']).replace(".", ","),
                "page1_field13": str(json_result['CA04']).replace(".", ","),
                "page1_field15": str(json_result['CA05']).replace(".", ","),
                "page1_field20": ""
            }
        )

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_115.pdf"

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500 ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel130DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration
    nombre = seller.name
    ejercicio = year

    # JSON Base
    jsonValues = {
        "nif": nif,
        "nombre": nombre,
        "ejercicio": ejercicio,
        "periodo": periodo,
    }
    print(f"JSON Base Inicial: {jsonValues}")

    # Obtener Casillas de Trimestre Anterior
    prev_ca07, prev_ca19 = prev_cas_model_130(seller, year, periodo)
    print(f"CASILLAS ANTERIORES - prev_ca07: {prev_ca07}, prev_ca19: {prev_ca19}")

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_130({seller.pk}, {year}, {latest_month}, {prev_ca07}, {prev_ca19});"
    print(f"SQL Generada: {sql}")
    json_result = exec_sql_secure(sql)
    print(f"Resultado SQL (JSON): {json_result}")

    if json_result is not None and json_result != "":
        # Verificar todas las casillas retornadas por la función SQL
        print("Valores de las Casillas del SQL:")
        for key in json_result:
            print(f" >> {key}: {json_result[key]}")

        # JSON PDF - Llenar los campos con los valores de las casillas
        jsonValues.update( 
            {
                "campo_01": str(json_result['CA01']).replace(".", ","),
                "campo_02": str(json_result['CA02']).replace(".", ","),
                "campo_03": str(json_result['CA03']).replace(".", ","),
                "campo_04": str(json_result['CA04']).replace(".", ","),
                "campo_05": str(json_result['CA05']).replace(".", ","),
                "campo_06": str(json_result['CA06']).replace(".", ","),
                "campo_07": str(json_result['CA07']).replace(".", ","),
                "campo_12": str(json_result['CA12']).replace(".", ","),
                "campo_13": str(json_result['CA13']).replace(".", ","),
                "campo_14": str(json_result['CA14']).replace(".", ","),
                "campo_15": str(json_result['CA15']).replace(".", ","),
                "campo_17": str(json_result['CA17']).replace(".", ","),
                "campo_19": str(json_result['CA19']).replace(".", ","),
                "Importe_ingreso": str(json_result['CA20']).replace(".", ",")
            }
        )

        print(f"JSON Valores Finales para el PDF: {jsonValues}")
    else:
        print("ERROR: El resultado de la consulta SQL es None o vacío.")

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:
            print(f"Intentando generar el PDF (Intento {trycount + 1})")
            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_130.pdf"
            print(f"Generando PDF en: {filePath}")

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500 ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)
            print(f"PDF Generado y aplanado correctamente.")

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF (Intento {trycount}): {str(e)}")
            time.sleep(0.1)

    if response:
        print("PDF Generado Correctamente.")
    else:
        print("Error: No se pudo generar el PDF después de 3 intentos.")

    return response

def calcModel303DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name
    ejercicio = year

    # JSON Base
    jsonValues = {
        "nif": nif,
        "nombre": nombre,
        "apellidos": nombre,
        "ejercicio": ejercicio,
        "periodo": periodo,
    }

    campo_87_anterior, campo_71_anterior = prev_cas_model_303(seller, year, periodo)

    print(f"Campo 87 Anterior: {campo_87_anterior}")
    print(f"Campo 71 Anterior: {campo_71_anterior}")

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_303({seller.pk}, {year}, {first_month}, {latest_month}, {campo_71_anterior}, {campo_87_anterior});"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):

        # JSON PDF
        jsonValues.update(
            {
                "campo_1": str(json_result['CA01']).replace(".", ","),
                "campo_2": str(json_result['CA02']).replace(".", ",") + "%",
                "campo_3": str(json_result['CA03']).replace(".", ","),
                "campo_4": str(json_result['CA04']).replace(".", ","),
                "campo_5": str(json_result['CA05']).replace(".", ",") + "%",
                "campo_6": str(json_result['CA06']).replace(".", ","),
                "campo_7": str(json_result['CA07']).replace(".", ","),
                "campo_8": str(json_result['CA08']).replace(".", ",") + "%",
                "campo_9": str(json_result['CA09']).replace(".", ","),
                "campo_10": str(json_result['CA10']).replace(".", ","),
                "campo_11": str(json_result['CA11']).replace(".", ","),
                "campo_12": str(json_result['CA12']).replace(".", ","),
                "campo_13": str(json_result['CA13']).replace(".", ","),
                "campo_14": str(json_result['CA14']).replace(".", ","),
                "campo_15": str(json_result['CA15']).replace(".", ","),
                "campo_16": str(json_result['CA16']).replace(".", ","),
                "campo_17": str(json_result['CA17']).replace(".", ",") + "%",
                "campo_18": str(json_result['CA18']).replace(".", ","),
                "campo_19": str(json_result['CA19']).replace(".", ","),
                "campo_20": str(json_result['CA20']).replace(".", ",") + "%",
                "campo_21": str(json_result['CA21']).replace(".", ","),
                "campo_22": str(json_result['CA22']).replace(".", ","),
                "campo_23": str(json_result['CA23']).replace(".", ",") + "%",
                "campo_24": str(json_result['CA24']).replace(".", ","),
                "campo_25": str(json_result['CA25']).replace(".", ","),
                "campo_26": str(json_result['CA26']).replace(".", ","),
                "campo_27": str(json_result['CA27']).replace(".", ","),
                "campo_28": str(json_result['CA28']).replace(".", ","),
                "campo_29": str(json_result['CA29']).replace(".", ","),
                "campo_32": str(json_result['CA32']).replace(".", ","),
                "campo_33": str(json_result['CA33']).replace(".", ","),
                "campo_36": str(json_result['CA36']).replace(".", ","),
                "campo_37": str(json_result['CA37']).replace(".", ","),
                "campo_40": str(json_result['CA40']).replace(".", ","),
                "campo_41": str(json_result['CA41']).replace(".", ","),
                "campo_45": str(json_result['CA45']).replace(".", ","),
                "campo_46": str(json_result['CA46']).replace(".", ","),
                "campo_59": str(json_result['CA59']).replace(".", ","),
                "campo_60": str(json_result['CA60']).replace(".", ","),
                "campo_120": str(json_result['CA120']).replace(".", ","),
                "campo_123": str(json_result['CA123']).replace(".", ","),
                "campo_124": str(json_result['CA124']).replace(".", ","),
                "campo_64": str(json_result['CA64']).replace(".", ","),
                "campo_66": str(json_result['CA66']).replace(".", ","),
                "campo_77": str(json_result['CA77']).replace(".", ","),
                "campo_110": str(json_result['CA110']).replace(".", ","),
                "campo_78": str(json_result['CA78']).replace(".", ","),
                "campo_87": str(json_result['CA87']).replace(".", ","),
                "campo_69": str(json_result['CA69']).replace(".", ","),
                "campo_71": str(json_result['CA71']).replace(".", ",")
            }
        )

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)
            writer.update_page_form_field_values(writer.pages[1], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_303.pdf"

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel309DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name
    ejercicio = year

    # JSON Base
    jsonValues = {
        "nif_1": nif,
        "apellidos_1": nombre,
        "nombre_1": nombre,
        "ejercicio": ejercicio,
        "periodo": periodo,
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_309({seller.pk}, {year}, {first_month}, {latest_month});"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):

        # JSON PDF
        jsonValues.update(
            {
                "casilla01": str(json_result['CA01']).replace(".", ","),
                "casilla02": str(json_result['CA02']).replace(".", ",") + "%",
                "casilla03": str(json_result['CA03']).replace(".", ","),
                "casilla10": str(json_result['CA10']).replace(".", ","),
                "casilla11": str(json_result['CA11']).replace(".", ",") + "%",
                "casilla12": str(json_result['CA12']).replace(".", ","),
                "casilla22": str(json_result['CA22']).replace(".", ","),
                "casilla24": str(json_result['CA24']).replace(".", ","),
                "seccion6_punto1": str(json_result.get('CA_SEC06_01', "")).replace(".", ",") if json_result.get('CA_SEC06_01') else "",
                "seccion6_punto2": str(json_result.get('CA_SEC06_02', "")).replace(".", ",") if json_result.get('CA_SEC06_02') else "",
                "seccion6_punto3": str(json_result.get('CA_SEC06_03', "")).replace(".", ",") if json_result.get('CA_SEC06_03') else "",
                "seccion6_punto4": str(json_result.get('CA_SEC06_04', "")).replace(".", ",") if json_result.get('CA_SEC06_04') else "",
                "seccion6_punto5": str(json_result.get('CA_SEC06_05', "")).replace(".", ",") if json_result.get('CA_SEC06_05') else "",
                "seccion6_punto6": str(json_result.get('CA_SEC06_06', "")).replace(".", ",") if json_result.get('CA_SEC06_06') else "",
            }
        )

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_309.pdf"

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel347DB(seller, writer, year, periodo):
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    telefono = seller.phone if seller.phone else ""
    nombre = seller.name
    ejercicio = year
    num_declarados = 0  # set as default
    importe_total_anual = 0  # set as default
    page = 1  # sets initial count as 1 page

    jsonValues = {
        "NIF_PAG1": nif,
        "NIF2_PAG1": nif,
        "TELEFONO_PAG1": telefono,
        "SELLER_PAG1": nombre,
        "EJERCICIO_PAG1": ejercicio,
    }

    # FIRST GETS CUSTOMERS AND PROVIDERS WHO MEET THE REQUIREMENTS FOR THE MODEL 347
    providers_invoices_347 = get_providers_model347(seller, year)
    customers_invoices_347 = get_customers_model347(seller, year)

    declarados = customers_invoices_347 + providers_invoices_347

    # CHECKS IF AT LEAST ONE EXIST IN ANY OF THE QUERIES
    if declarados:
        num_declarados = len(declarados)
        page = 2  # los declarados empiezan en la página 2
        n_dec = 1  # numero de declarados por página son 3

        for index, declarado in enumerate(declarados, start=1):
            # importes = get_total_sum_in_periods_model_347(declarado, seller, accounting_year=ejercicio)

            country_declarado = Country.objects.filter(iso_code=declarado['country']).first()
            if country_declarado and country_declarado.iso_code == 'ES':
                jsonValues.update({
                    f"NIF_DECLARADO{n_dec}_PAG{page}": declarado['nif_cif_iva'] if declarado['nif_cif_iva'] else "",
                    f"PROVINCIA{n_dec}_PAG{page}": declarado['zip'][:2]
                })
            elif country_declarado and country_declarado.is_european_union:
                if declarado['nif_cif_iva'] and declarado['nif_cif_iva'][:2] == country_declarado.iso_code:
                    declarado['nif_cif_iva'] = declarado['nif_cif_iva'][2:]

                jsonValues.update({
                    f"NIF_IVA{n_dec}_PAG{page}": declarado['nif_cif_iva'] if declarado['nif_cif_iva'] else "",
                    f"PROVINCIA{n_dec}_PAG{page}": "99",
                    f"PAIS{n_dec}_PAG{page}": declarado['country']
                })
            else:
                jsonValues.update({
                    f"PROVINCIA{n_dec}_PAG{page}": "99",
                    f"PAIS{n_dec}_PAG{page}": declarado['country']
                })

            jsonValues.update({
                f"NIF_PAG{page}": nif,
                f"EJ_PAG{page}": ejercicio,
                f"SELLER{n_dec}_PAG{page}": declarado['name'],
                f"CLAVE{n_dec}_PAG{page}": declarado['clave'][6:7],
                f"IMPORTE_ANUAL{n_dec}_PAG{page}": str(declarado["total_euros"]).replace(".", ","),
                f"T1_{n_dec}_PAG{page}": str(declarado["q1_total"]).replace(".", ","),
                f"T2_{n_dec}_PAG{page}": str(declarado["q2_total"]).replace(".", ","),
                f"T3_{n_dec}_PAG{page}": str(declarado["q3_total"]).replace(".", ","),
                f"T4_{n_dec}_PAG{page}": str(declarado["q4_total"]).replace(".", ","),
            })
            importe_total_anual += declarado["total_euros"]
            if n_dec == 3 and index != len(declarados):
                page += 1
                n_dec = 0
            n_dec += 1
        importe_total_anual = round(importe_total_anual, 2)
    jsonValues.update({
        "C01_PAG1": num_declarados,
        "C02_PAG1": str(importe_total_anual).replace(".", ",")
    })

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # WRITE THE PAGES
            writer2 = PdfWriter()
            for i in range(page):
                writer2.add_page(writer.pages[i])
                writer2.update_page_form_field_values(writer2.pages[i], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_347.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500 ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel349DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)

    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name.replace("'", "")
    ejercicio = year
    last_page = 9

    # JSON Base
    jsonValues = {
        "doc_0_nombre": nombre,
        "doc_0_nif": nif,
        "doc_0_periodo": periodo,
        "doc_0_ejercicio": ejercicio,
    }
    for x in range(1, 10):
        jsonValues.update(
            {
                f"doc_{x}_nif_2": nif,
                f"doc_{x}_periodo_2": periodo,
            }
        )
        x += 1

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_349({seller.pk}, {year}, {first_month}, {latest_month});"
    # print("sql: ", sql)
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):

        # JSON PDF
        jsonValues.update( 
            {
                "doc_0_resumen_01": str(json_result['CA01']).replace(".", ","),
                "doc_0_resumen_02": str(json_result['CA02']).replace(".", ","),
                "LAST_PAGE": json_result['LAST_PAGE']
            }
        )

        try:
            for key, value in json_result.items():
                if key != 'CA01' and key != 'CA02' and key != 'shortname' and key != 'LAST_PAGE':
                    jsonValues[key] = value
                elif key == 'LAST_PAGE':
                    last_page = value
        except Exception as e:
            print(f"Error al actualizar jsonValues: {e}")

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # CREATE WRITER
            writer2 = PdfWriter()

            # ADD PAGES
            writer2.add_page(writer.pages[0])
            if last_page > 9:
                last_page = 9
            for y in range(last_page):
                writer2.add_page(writer.pages[y + 1])

            # SET/UPDATE JSON VALUES TO PDF
            writer2.update_page_form_field_values(writer2.pages[0], jsonValues)
            for y in range(last_page):
                writer2.update_page_form_field_values(writer2.pages[y + 1], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_349.pdf"

            # ADD JSON VALUES TO METADATA
            json_str = json.dumps(jsonValues)
            writer2.add_metadata({"/JsonValues": json_str})

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel369DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)

    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name.replace("'", "")
    ejercicio = year
    last_page = 2

    # JSON Base
    jsonValues = {
        "doc_0_nombre": nombre,
        "doc_0_nif": nif,
        "doc_0_ejercicio": ejercicio,
        "doc_0_periodo": periodo,
        "doc_1_nif_2": nif,
        "doc_1_nombre_2": nombre,
        "doc_2_NIF_pag4": nif,
        "doc_2_Nombre_pag4": nombre,
        "doc_3_NIF_pag4": nif,
        "doc_3_Nombre_pag4": nombre,
        "doc_4_NIF_pag4": nif,
        "doc_4_Nombre_pag4": nombre,
        "doc_5_NIF_pag4": nif,
        "doc_5_Nombre_pag4": nombre,
        "doc_6_nif_3": nif,
        "doc_6_nombre_3": nombre,
        "doc_6_resultado_total": 0
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_369({seller.pk}, {year}, {first_month}, {latest_month});"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):

        # JSON PDF
        errors = None
        try:
            for key, value in json_result.items():
                if key != 'shortname' and key != 'T6PAG' and key != 'ERRORS':
                    jsonValues[key] = value.replace(",", "").replace(".", ",")
                elif key == 'T6PAG':
                    last_page = value
                elif key == 'ERRORS':
                    errors = value
        except Exception as e:
            print(f"Error al actualizar jsonValues: {e}")

    # print("json_values: ", jsonValues)

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:
            # CREATE WRITER
            writer2 = PdfWriter()

            w2pag = 0

            # ADD ERROR PAGE
            # errors = "ERROR: Hay un problema con  Tax Country: FR Departure Country: ES y IVA: 21.00%.ERROR: Hay un problema con  Tax Country: IT Departure Country: ES y IVA: 21.00%.ERROR: Hay un problema con  Tax Country: BE Departure Country: IT y IVA: 22.00%.ERROR: Hay un problema con  Tax Country: FR Departure Country: IT y IVA: 22.00%."
            if (errors is not None and errors != ""):
                try:
                    err_obj = {}
                    split = errors.split("ERROR:")
                    for x in range(0, len(split)):
                        err = split[x]
                        err_obj[f"Error_{x + 1}"] = err
                    reader = PdfReader("/app/muaytax/static/assets/pdf/error.pdf")
                    writer2.add_page(reader.pages[0])
                    writer2.update_page_form_field_values(writer2.pages[w2pag], err_obj)
                    w2pag += 1
                except Exception as e:
                    print(f"Error al agregar pagina de errores: {e}")

            # ADD PAGE 0 & SET/UPDATE JSON VALUES TO PDF
            writer2.add_page(writer.pages[0])
            writer2.update_page_form_field_values(writer2.pages[w2pag], jsonValues)
            w2pag += 1

            # ADD PAGE 1 & SET/UPDATE JSON VALUES TO PDF
            writer2.add_page(writer.pages[1])
            writer2.update_page_form_field_values(writer2.pages[w2pag], jsonValues)
            w2pag += 1

            if last_page < 2:
                last_page = 2
            if last_page > 5:
                last_page = 5

            # ADD PAGES & SET/UPDATE JSON VALUES TO PDF
            for y in range(2, last_page + 1):
                writer2.add_page(writer.pages[y])
                writer2.update_page_form_field_values(writer2.pages[w2pag], jsonValues)
                w2pag += 1

            # ADD PAGE 6 & SET/UPDATE JSON VALUES TO PDF
            writer2.add_page(writer.pages[6])
            writer2.update_page_form_field_values(writer2.pages[w2pag], jsonValues)

            # ADD JSON VALUES TO METADATA
            json_str = json.dumps(jsonValues)
            writer2.add_metadata({"/JsonValues": json_str})

            # WRITE PDF FILE
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_369.pdf"
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RETURN PDF FILE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel390DB(seller, writer, reader, year, periodo):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nif = nif if nif else ""
    nombre = seller.name if seller.name else ""
    ejercicio = year if year else ""

    first_month = 1
    latest_month = 12

    jsonValues = {
        "EJERCICIO": ejercicio,
        "NIF_pag1": nif,
        "ejercicio": ejercicio,
        "nombre_seller_pag1": nombre,
        "concurso_no": "/concurso_no",
        "caja_no": "/caja_no",
        "Destinatario_caja_no": "/Destinatario_caja_no",
        "NIF_pag2": nif,
        "nombre_seller_pag2": nombre,
        "NIF_pag3": nif,
        "nombre_Seller_pag_3": nombre,
        "NIF_pag4": nif,
        "nombre_Seller_pag_4": nombre,
        "NIF_pag6": nif,
        "nombre_Seller_pag_6": nombre,
        "NIF_pag7": nif,
        "nombre_Seller_pag_7": nombre,
        "POR_PODER_D_1": f"{seller.name_representative if seller.name_representative else ''} {seller.last_name_representative if seller.last_name_representative else ''}",
        "POR_PODER_NIF_1": seller.representative_id if seller.representative_id else "",
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_390({seller.pk}, {year}, {first_month}, {latest_month})"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        # -----------JSON PDF---------

        # IAE principal con un solo IAE
        if json_result['TOTAL_IAE'] == 1 or json_result['TOTAL_IAE'] == 0:
            # Eliminar el parentesis y todo lo que hay despues
            # iae_name_clean = re.sub(r"\(.*", "", json_result['iae_name']).strip()
            jsonValues.update(
                {
                    "actividad_principal_nombre": json_result['iae_name'] if json_result['iae_name'] else "",
                    "actividad_principal_codigo": json_result['iae_code'] if json_result['iae_code'] else "",
                    "actividad_principal_epigrafe": json_result['iae_id'].replace("ES-", "") if json_result[
                        'iae_id'] else ""
                }
            )


        # IAEs cuando tiene más de uno
        elif json_result['TOTAL_IAE'] > 1:
            iae = '1'
            for clave, valor in json_result.items():
                if valor == 'ES-665':
                    iae = clave[-1]
                    break

            # IAE principal
            iae_desc = EconomicActivity.objects.filter(code=json_result[f'IAE_{iae}']).first()
            jsonValues.update(
                {
                    "actividad_principal_nombre": iae_desc if iae_desc else "",
                    "actividad_principal_codigo": iae_desc.activity_code if iae_desc and iae_desc.activity_code else "",
                    "actividad_principal_epigrafe": json_result[f'IAE_{iae}'].replace("ES-", "") if json_result[
                        f'IAE_{iae}'] else ""
                }
            )
            # IAE OTRAS
            other_iae = 1
            while f"CA114_{other_iae}" in json_result:
                if str(other_iae) != iae:
                    iae_desc = EconomicActivity.objects.filter(code=json_result[f'IAE_{other_iae}']).first()
                    # IAE otras - Datos estadísticos
                    jsonValues.update(
                        {
                            f"actividad_otras{other_iae}_nombre": iae_desc if iae_desc else "",
                            f"actividad_otras{other_iae}_codigo": iae_desc.activity_code if iae_desc and iae_desc.activity_code else "",
                            f"actividad_otras{other_iae}_epigrafe": json_result[f'IAE_{other_iae}'].replace("ES-",
                                                                                                            "") if
                            json_result[f'IAE_{other_iae}'] else ""
                        }
                    )
                # IAE otras - Sección 12 Prorrata
                jsonValues.update(
                    {
                        f"casilla_114_{other_iae}": str(json_result[f'CA114_{other_iae}']).replace(".", ","),
                        f"casilla_115_{other_iae}": str(json_result[f'CA115_{other_iae}']).replace(".", ","),
                        f"casilla_116_{other_iae}": "-",
                        f"casilla_117_{other_iae}": str(json_result[f'CA117_{other_iae}']).replace(".", ","),
                        f"casilla_118_{other_iae}": str(json_result[f'CA118_{other_iae}']).replace(".", ","),
                    }
                )
                other_iae += 1

        # ASIGNAR VALORES DE JSON A PDF
        jsonValues.update(
            {"casilla_01": str(json_result['CA001']).replace(".", ","),
             "casilla_02": str(json_result['CA002']).replace(".", ","),
             "casilla_03": str(json_result['CA003']).replace(".", ","),
             "casilla_04": str(json_result['CA004']).replace(".", ","),
             "casilla_05": str(json_result['CA005']).replace(".", ","),
             "casilla_06": str(json_result['CA006']).replace(".", ","),
             "casilla_25": str(json_result['CA025']).replace(".", ","),
             "casilla_26": str(json_result['CA026']).replace(".", ","),
             "casilla_551": str(json_result['CA551']).replace(".", ","),
             "casilla_552": str(json_result['CA552']).replace(".", ","),
             "casilla_27": str(str(json_result['CA027']).replace(".", ",")),
             "casilla_28": str(json_result['CA028']).replace(".", ","),
             "casilla_29": str(json_result['CA029']).replace(".", ","),
             "casilla_30": str(json_result['CA030']).replace(".", ","),
             "casilla_33": str(json_result['CA033']).replace(".", ","),
             "casilla_34": str(json_result['CA034']).replace(".", ","),
             "casilla_35": str(json_result['CA035']).replace(".", ","),
             "casilla_36": str(json_result['CA036']).replace(".", ","),
             "casilla_599": str(json_result['CA599']).replace(".", ","),
             "casilla_600": str(json_result['CA600']).replace(".", ","),
             "casilla_601": str(json_result['CA601']).replace(".", ","),
             "casilla_602": str(json_result['CA602']).replace(".", ","),
             "casilla_43": str(json_result['CA043']).replace(".", ","),
             "casilla_44": str(json_result['CA044']).replace(".", ","),
             "casilla_47": str(json_result['CA047']).replace(".", ","),
             "casilla_190": str(json_result['CA190']).replace(".", ","),
             "casilla_191": str(json_result['CA191']).replace(".", ","),
             "casilla_603": str(json_result['CA603']).replace(".", ","),
             "casilla_604": str(json_result['CA604']).replace(".", ","),
             "casilla_605": str(json_result['CA605']).replace(".", ","),
             "casilla_606": str(json_result['CA606']).replace(".", ","),
             "casilla_48": str(json_result['CA048']).replace(".", ","),
             "casilla_49": str(json_result['CA049']).replace(".", ","),
             "casilla_202": str(json_result['CA202']).replace(".", ","),
             "casilla_203": str(json_result['CA203']).replace(".", ","),
             "casilla_619": str(json_result['CA619']).replace(".", ","),
             "casilla_620": str(json_result['CA620']).replace(".", ","),
             "casilla_621": str(json_result['CA621']).replace(".", ","),
             "casilla_622": str(json_result['CA622']).replace(".", ","),
             "casilla_52": str(json_result['CA052']).replace(".", ","),
             "casilla_53": str(json_result['CA053']).replace(".", ","),
             "casilla_629": str(json_result['CA629']).replace(".", ","),
             "casilla_630": str(json_result['CA630']).replace(".", ","),
             "casilla_52": str(json_result['CA052']).replace(".", ","),
             "casilla_53": str(json_result['CA053']).replace(".", ","),
             "casilla_629": str(json_result['CA629']).replace(".", ","),
             "casilla_630": str(json_result['CA630']).replace(".", ","),
             "casilla_56": str(json_result['CA056']).replace(".", ","),
             "casilla_57": str(json_result['CA057']).replace(".", ","),
             "casilla_637": str(json_result['CA637']).replace(".", ","),
             "casilla_638": str(json_result['CA638']).replace(".", ","),
             "casilla_597": str(json_result['CA597']).replace(".", ","),
             "casilla_598": str(json_result['CA598']).replace(".", ","),
             "casilla_64": str(json_result['CA064']).replace(".", ","),
             "casilla_65": str(json_result['CA065']).replace(".", ","),
             "casilla_84": str(json_result['CA084']).replace(".", ","),
             "casilla_85": str(json_result['CA085']).replace(".", ","),
             "casilla_86": str(json_result['CA086']).replace(".", ","),
             "casilla_95": str(json_result['CA095']).replace(".", ","),
             "casilla_97": str(json_result['CA097']).replace(".", ","),
             "casilla_98": str(json_result['CA098']).replace(".", ","),
             "casilla_662": str(json_result['CA662']).replace(".", ",").replace("-", ""),
             "casilla_99": str(json_result['CA099']).replace(".", ","),
             "casilla_103": str(json_result['CA103']).replace(".", ","),
             "casilla_104": str(json_result['CA104']).replace(".", ","),
             "casilla_110": str(json_result['CA110']).replace(".", ","),
             "casilla_125": str(json_result['CA125']).replace(".", ","),
             "casilla_126": str(json_result['CA126']).replace(".", ","),
             "casilla_127": str(json_result['CA127']).replace(".", ","),
             "casilla_108": str(json_result['CA108']).replace(".", ","),
             "casilla_230": str(json_result['CA230']).replace(".", ","),
             "casilla_231": str(json_result['CA231']).replace(".", ","),

             "casilla_639": str(json_result['CA639']).replace(".", ","),
             "casilla_62": str(json_result['CA062']).replace(".", ","),
             }
        )

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:
            # SET/UPDATE JSON VALUES TO PDF
            for i in range(len(reader.pages)):
                writer.update_page_form_field_values(writer.pages[i], jsonValues)

            # WRITE PDF FILE
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_390.pdf"
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RETURN PDF FILE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')
        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF (intento {trycount}): {e}")
            time.sleep(0.1)

    return response

def calModel180DB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name
    ejercicio = year
    first_month = 1
    latest_month = 12

    # JSON Base
    jsonValues = {
        "NIF_pag1": nif,
        "Nombre_seller_pag1": nombre,
        "Nombre_contacto_pag1": "Muay Tax Advisors, SL",
        "Telefono_contacto_pag1": "*********",
        "ejercicio_pag1": ejercicio,
        "NIF_pag2": nif,
        "ejercicio_pag2": ejercicio,
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_180({seller.pk}, {year}, {first_month}, {latest_month})"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        # -----------JSON PDF---------
        jsonValues.update(
            {
                "total_perceptores_pag1": str(json_result['CA01']).replace(".", ","),
                "base_total_retenciones_pag1": str(json_result['CA02']).replace(".", ","),
                "IVA_total_modelo_pag1": str(json_result['CA03']).replace(".", ","),
            }
        )

        page = 2
        x = 1
        # Alquileres
        while f"NIF_PROV_{x}_PAG_{page}" in json_result:
            jsonValues.update(
                {
                    f"NIF_perceptor_{x}_pag{page}": json_result[f"NIF_PROV_{x}_PAG_{page}"],
                    f"nombre_seller_{x}_pag{page}": json_result[f"NOMBRE_PROV_{x}_PAG_{page}"],
                    f"provincia_codigo_{x}_pag{page}": json_result[f"COD_PROVINCIA_{x}_PAG_{page}"],
                    f"Modalidad_{x}_pag{page}": json_result[f"MODALIDAD_{x}_PAG_{page}"],
                    f"base_retenciones_{x}_pag{page}": json_result[f"BASE_RETENCIONES_{x}_PAG_{page}"],
                    f"porcentaje_retencion_{x}_pag{page}": json_result[f"PORCENTAJE_{x}_PAG_{page}"],
                    f"retenciones_a_cuenta_{x}_pag{page}": json_result[f"SUMA_RETENCIONES_{x}_PAG_{page}"],
                    f"ejercicio_devengo_{x}_pag{page}": json_result[f"EJERCICIO_DEVENGO_{x}_PAG_{page}"],
                    f"situacion_codigo_{x}_pag{page}": json_result[f"SITUACION_{x}_PAG_{page}"],
                    f"referencia_catastral_{x}_pag{page}": json_result[f"REF_CATASTRAL_{x}_PAG_{page}"],
                    f"tipo_de_via_{x}_pag{page}": json_result[f"TIPO_VIA_{x}_PAG_{page}"],
                    f"nombre_via_{x}_pag{page}": json_result[f"NOMBRE_VIA_{x}_PAG_{page}"],
                    f"tipo_num_{x}_pag{page}": json_result[f"TIPO_NUM_{x}_PAG_{page}"],
                    f"num_casa_{x}_pag{page}": json_result[f"NUM_CASA_{x}_PAG_{page}"],
                    f"calif_num_{x}_pag{page}": json_result[f"CALIF_NU_{x}_PAG_{page}"],
                    f"bloque_{x}_pag{page}": json_result[f"BLOQUE_{x}_PAG_{page}"],
                    f"portal_{x}_pag{page}": json_result[f"PORTAL_{x}_PAG_{page}"],
                    f"escalera_{x}_pag{page}": json_result[f"ESCALERA_{x}_PAG_{page}"],
                    f"planta_{x}_pag{page}": json_result[f"PLANTA_{x}_PAG_{page}"],
                    f"puerta_{x}_pag{page}": json_result[f"PUERTA_{x}_PAG_{page}"],
                    f"complemento_domicilio_{x}_pag{page}": json_result[f"COMP_DOM_{x}_PAG_{page}"],
                    f"localidad_{x}_pag{page}": json_result[f"LOCALIDAD_{x}_PAG_{page}"],
                    f"nombre_municipio_{x}_pag{page}": json_result[f"NOMBRE_MUNICIPIO_{x}_PAG_{page}"],
                    f"cod_municipio_{x}_pag{page}": json_result[f"CODIGO_MUNICIPIO_{x}_PAG_{page}"],
                    f"provincia_perceptor{x}_{x}_pag{page}": json_result[f"PROVINCIA_{x}_PAG_{page}"],
                    f"cod_provincia_{x}_pag{page}": json_result[f"COD_PROVINCIA_{x}_PAG_{page}"],
                    f"cod_postal_{x}_pag{page}": json_result[f"COD_POSTAL_{x}_PAG_{page}"],
                }
            )
            x += 1
            if x == 5:
                x = 1
                page += 1

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:
            last_page = 2
            json_str = json.dumps(jsonValues)

            writer2 = PdfWriter()

            for y in range(last_page):
                writer2.add_page(writer.pages[y])

            for y in range(last_page):
                writer2.update_page_form_field_values(writer2.pages[y], jsonValues)

            writer2.add_metadata({
                "/JsonValues": json_str
            })

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_180.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RETURN PDF FILE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calModel184DB(seller, writer, year, period):
    def assign_rent_fields(rent, page_num, suffix=''):
        return {
            f"CLAVE{suffix}_PAG{page_num}": 'D',
            f"SUBCLAVE{suffix}_PAG{page_num}": '01' if rent.country.iso_code == 'ES' else '02',
            f"PAIS{suffix}_PAG{page_num}": rent.country.iso_code,
            f"TIPO_ACTIVIDAD{suffix}_PAG{page_num}": rent.economic_activity_type or '',
            f"EPIGRAFE{suffix}_PAG{page_num}": rent.epigraph_iae.code.split('-')[-1] if rent.epigraph_iae else '',
            f"REGIMEN{suffix}_PAG{page_num}": '1',
            f"INGRESOS{suffix}_PAG{page_num}": rent.full_income,
            f"DETALLES{suffix}_AE1_PAG{page_num}": rent.personal_expenses,
            f"DETALLES{suffix}_AE2_PAG{page_num}": rent.operating_expenses,
            f"DETALLES{suffix}_AE3_PAG{page_num}": rent.deductible_taxes,
            f"DETALLES{suffix}_AE4_PAG{page_num}": rent.rental_expenses_fee,
            f"DETALLES{suffix}_AE5_PAG{page_num}": rent.repair_costs,
            f"DETALLES{suffix}_AE6_PAG{page_num}": rent.professional_services,
            f"DETALLES{suffix}_AE7_PAG{page_num}": rent.supplies,
            f"DETALLES{suffix}_AE8_PAG{page_num}": rent.financial_expenses,
            f"DETALLES{suffix}_AE11_PAG{page_num}": rent.other_deductibe_expenses,
            f"RENTA_ATRIBUIBLE{suffix}_PAG{page_num}": rent.total_benefits,
        }

    nombre_y_apellidos = f"{seller.user.last_name or ''} {seller.user.first_name or ''}" if seller.user else ''
    vat_spain = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    # nif_spain = vat_spain.vat_number if vat_spain else seller.nif_registration
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and vat_spain is not None:
        nif_spain = vat_spain.vat_number
    else:
        nif_spain = seller.nif_registration

    # JSON Base
    jsonValues = {
        "EJERCICIO_PAG1": year,
        "NIF1_PAG1": nif_spain,
        "DENOMINACION_ENTIDAD_PAG1": seller.name,
        "SELLER1_PAG1": nombre_y_apellidos,
        "TELEFONO_PAG1": seller.contact_phone or '',
        "NIF2_PAG1": seller.representative_id or '',
        "SELLER2_PAG1": nombre_y_apellidos,
        "TIPO_ENTIDAD2_PAG1": '1',
        "OBJETO_PAG1": 'A',
        "PAIS_PAG1": 'US',
        "PORCENTAJE_PAG1": seller.percent_entity
    }

    model184 = PresentedM184.objects.filter(
        seller=seller,
        year=year,
        is_processed=True
    ).first()

    print(f"model184: {model184}")

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET PAGES
            writer2 = PdfWriter()
            writer2.add_page(writer.pages[0]) # Añadir la 1a pagina
            writer2.update_page_form_field_values(writer2.pages[0], jsonValues) #Rellenar la primera página 

            # SET/UPDATE JSON VALUES TO PDF
            if model184:
                rents = model184.accounting_information.all()
                partners = model184.partners.all()

                jsonValues.update({
                    "IMPORTE_PAG1": sum([x.full_income for x in rents]),
                    "TOTAL_REGISTROS_PAG1": partners.count(),
                })

                rents_pages = math.ceil(len(rents) / 2)
                partners_pages = math.ceil(len(partners) / 4)

                total_pages = 1 + rents_pages + partners_pages

                ######### AÑADIR PAGINAS CON LAS RENTAS Y SUS VALORES ######### 
                page = 2
                x = 1
                rent_actual = 0
                while rent_actual !=len(rents):
                    jsonValues.update({
                        f"NIF_PAG{page}": nif_spain,
                        f"DENOMINACION_ENTIDAD_PAG{page}": seller.name,
                        f"EJERCICIO_PAG{page}": year,
                        f"HOJA_PAG{page}": page,
                        f"HOJA2_PAG{page}": total_pages,
                    })
                    jsonValues.update(assign_rent_fields(rents[rent_actual], page, str(x)))
                    x += 1
                    rent_actual += 1
                    if x == 3:
                        x = 1
                        page += 1
                
                for y in range(1, rents_pages + 1):
                    writer2.add_page(writer.pages[y])
                
                for y in range(1, rents_pages + 1):
                    writer2.update_page_form_field_values(writer2.pages[y], jsonValues)


                ######### AÑADIR PAGINAS CON LOS SOCIOS Y SUS VALORES #########

                page = 10
                x = 1
                actual_page = 2 + rents_pages
                entity_total_benefits = float(sum([x.total_benefits for x in rents]))
                # getcontext().prec = 1  # necesario para hallar la mejor precision en el porcentaje
                for i, partner in enumerate(partners, start=1):
                    import_partner = Decimal(partner.shares_percentage) / Decimal(100) * Decimal(entity_total_benefits)
                    jsonValues.update({
                        f"NIF_PAG{page}": nif_spain,
                        f"EJERCICIO_PAG{page}": year,
                        f"HOJA_PAG{page}": actual_page,
                        f"HOJA2_PAG{page}": total_pages,
                        f"NIF{x}_PAG{page}": partner.id_number,
                        f"NIF_REPRESENTANTE{x}_PAG{page}": partner.legal_representative or '',
                        f"SELLER{x}_PAG{page}": partner.last_name + ' ' + partner.name,
                        f"PROVINCIA{x}_PAG{page}": partner.fiscal_province.code,
                        f"CLAVE_TIPO{x}_PAG{page}": '1',
                        f"CLAVE_PAIS{x}_PAG{page}": partner.address.address_country.iso_code if partner.address.address_country.iso_code != 'ES' else '',
                        f"X{x}_PAG{page}": 'X',
                        f"DIAS{x}_PAG{page}": partner.days_member,
                        f"PARTICIPACION{x}_PAG{page}": partner.shares_percentage,
                        f"CLAVE{x}_PAG{page}": 'D',
                        f"IMPORTE{x}_PAG{page}": import_partner.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                        f"DOMICILIO{x}_PAG{page}": f"{partner.address.address_city} ({partner.address.address_state}) {partner.address.address_zip}",
                    })
                    x += 1
                    if x == 5:
                        x = 1
                        page += 1
                        actual_page += 1
                
                for y in range(9, partners_pages + 9):
                    writer2.add_page(writer.pages[y])

                for y in range(1 + rents_pages, total_pages):
                    writer2.update_page_form_field_values(writer2.pages[y], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{period}_184.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RETURN PDF FILE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel190DB(seller, writer, year, periodo):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name
    ejercicio = year
    first_month = 1
    latest_month = 12

    # JSON Base
    jsonValues = {
        "doc_1_ejercicio": ejercicio,
        "doc_1_nif": nif,
        "doc_1_nombre": nombre
    }

    # Obtener Casillas de Trimestre Actual
    sql = f"SELECT func_calc_model_es_190({seller.pk}, {year}, {first_month}, {latest_month})"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        # -----------JSON PDF---------
        jsonValues.update(
            {
                "doc_1_total_perceptores": str(json_result['CA01']).replace(".", ","),
                "doc_1_importe_percepciones": str(json_result['CA02']).replace(".", ","),
                "doc_1_importe_retenciones": str(json_result['CA03']).replace(".", ","),
            }
        )
        page = 2
        x = 1
        # Perceptores
        while f"doc_{page}_nif_op_{x}" in json_result:
            jsonValues.update(
                {
                    f"doc_{page}_nif": nif,
                    f"doc_{page}_ejercicio": ejercicio,
                    f"doc_{page}_nif_op_{x}": json_result[f"doc_{page}_nif_op_{x}"],
                    f"doc_{page}_nombre_op_{x}": json_result[f"doc_{page}_name_op_{x}"],
                    f"doc_{page}_provincia_op_{x}": json_result[f"doc_{page}_provincia_op_{x}"],
                    f"doc_{page}_clave_op_{x}": json_result[f"doc_{page}_clave_op_{x}"],
                    f"doc_{page}_subclave_op_{x}": json_result[f"doc_{page}_subclave_op_{x}"],
                    f"doc_{page}_base_op_{x}": json_result[f"doc_{page}_base_imponible_op_{x}"],
                    f"doc_{page}_irpf_op_{x}": json_result[f"doc_{page}_irpf_op_{x}"],
                    f"doc_{page}_ejercicio_op_{x}": ejercicio,
                    f"doc_{page}_nacimiento_op_{x}": json_result[f"doc_{page}_nacimiento_op_{x}"],
                    f"doc_{page}_sit_familiar_op_{x}": json_result[f"doc_{page}_sit_familiar_op_{x}"],
                    f"doc_{page}_nif_conyuge_op_{x}": json_result[f"doc_{page}_nif_conyuge_op_{x}"],
                    f"doc_{page}_titularidad_op_{x}": json_result[f"doc_{page}_titularidad_op_{x}"],
                    f"doc_{page}_discapacidad_op_{x}": json_result[f"doc_{page}_discapacidad_op_{x}"],
                    f"doc_{page}_contrato_op_{x}": json_result[f"doc_{page}_contrato_op_{x}"],
                    f"doc_{page}_movilidad_op_{x}": json_result[f"doc_{page}_movilidad_op_{x}"]
                }
            )
            x += 1
            if x == 3:
                x = 1
                page += 1

        last_page = json_result['LAST_PAGE']

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET PAGES
            writer2 = PdfWriter()

            if last_page > 10:
                last_page = 10

            for y in range(last_page):
                writer2.add_page(writer.pages[y])

            # SET/UPDATE JSON VALUES TO PDF
            for y in range(last_page):
                writer2.update_page_form_field_values(writer2.pages[y], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_190.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RETURN PDF FILE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModel202DB(seller, writer, reader, year, periodo):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
    if (seller.legal_entity == 'llc' or seller.legal_entity == 'other') and sellervat is not None:
        nif = sellervat.vat_number
    else:
        nif = seller.nif_registration

    nombre = seller.name
    ejercicio = year
    period = ''
    year_dec = None
    cnae = ''
    per_imp = f"01/01/{year}"

    # Calcular el año y el período de la declaración
    if periodo == 'M4':
        period = '1P'
    elif periodo == 'M10':
        period = '2P'
    elif periodo == 'M12':
        period = '3P'
    
    year_dec = int(year) - 2 if periodo == 'M4' else int(year) -1
    
    # JSON Base
    jsonValues = {
        "pag0_nif": nif,
        "pag0_name": nombre,
        "pag0_year": ejercicio,
        "pag0_period": period,
        "pag1_nif": nif,
        "pag1_name": nombre,
        "pag0_per_imp": per_imp,
        "pag0_dat_ad_11": "X"
    }

    # OBTENER VALORES DE LAS CASILLAS
    yieldRecords = SellerYieldRecord.objects.filter(seller=seller, year=year_dec, period=periodo).first()
    vat_activity = SellerVatActivity.objects.filter(sellervat__seller=seller, sellervat__vat_country__iso_code = 'ES')
    
    if vat_activity is not None:
        if vat_activity.count() == 1:
            vat_activity = vat_activity.first()
        else:
            vat_activity = vat_activity.filter(sellervat_activity_iae__code = 'ES-665').first()
        
        cnae= vat_activity.sellervat_activity_iae.cnae if vat_activity is not None else ''


    if yieldRecords is not None:
        jsonValues.update(
            {
                "pag0_cnae": cnae,
                "pag0_dat_ad_10": str(yieldRecords.assessment_type).replace(".", ",") + "%",
                "pag0_ca01": str(yieldRecords.net_yields).replace(".", ","),
                "pag0_ca03": str(round((float(yieldRecords.net_yields) * float(yieldRecords.assessment_type/100 if yieldRecords.assessment_type else 0)), 2)).replace(".", ","),
                "pag1_total": str(round((float(yieldRecords.net_yields) * float(yieldRecords.assessment_type/100 if yieldRecords.assessment_type else 0)), 2)).replace(".", ","),
            }
        )


    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:
            
            # SET/UPDATE JSON VALUES TO PDF
            for i in range(len(reader.pages)):
                writer.update_page_form_field_values(writer.pages[i], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_ES_{year}_{periodo}_202.pdf"

            # WIRTE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500 ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount} - Detalles del error: {e}")
            time.sleep(0.1)

    return response

def calcModelITLIPEDB(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()
    representative = None
    if sellervat is not None:
        representative = Representative.objects.filter(
            pk=sellervat.vat_representative_id, type_representation = 'legal_representative').first() if sellervat.vat_representative_id is not None else ''
    
    codice_fiscale = ''
    if seller.legal_entity in ['self-employed', 'self-employed-outside']:
        codice_fiscale = sellervat.codice_fiscale.replace("IT", "") if sellervat and sellervat.codice_fiscale is not None else ''
    else:
        codice_fiscale = sellervat.vat_number.replace("IT", "") if sellervat and sellervat.vat_number is not None else ''
    
    anno = year
    partita_iva = sellervat.vat_number.replace("IT", "") if sellervat and sellervat.vat_number is not None else ''
    nombre = seller.name
    trimestre = periodo.replace("T", "")
    codice_fiscale_dec = ''
    codice_carica = ''
    codice_fiscale_soc_dichiarante = ''
    firma = ''

    if trimestre == '4':
        trimestre = '5'

    if sellervat and sellervat.it_representation_type == '1':
        # codice_fiscale_dec = sellervat.it_fiscal_representative
        # firma = sellervat.it_fiscal_representative_name
        if representative is not None:
            codice_fiscale_dec = representative.codice_fiscale
            firma = f"{representative.first_name.upper()} {representative.last_name.upper()}" if representative.first_name and representative.last_name else ''
        codice_carica = '1'
        codice_fiscale_soc_dichiarante = partita_iva
    elif sellervat and sellervat.it_representation_type == '2':
        codice_fiscale_dec = '****************'
        codice_carica = '6'
        codice_fiscale_soc_dichiarante = '13007250965'
        firma = 'Alberto Zamora'

    codice_fiscale_incaricato = '13007250965'
    impegno_pres = '2'
    data_impegno_pres = f"{datetime.now().day}/{datetime.now().month}/{datetime.now().year}"
    firma_incaricato = 'Alberto Zamora'

    # Obtener datos casillas trimestres anteriores
    prev_vp14_1 = 0.0
    prev_vp14_2 = 0.0
    acconto_iva = 0.0
    period = int(periodo.replace("T", "")) - 1

    # trimestre anterior (4o trimestre) del año pasado
    if period == 0:
        model = PresentedModel.objects.filter(
            seller_id=seller.pk,
            year=(int(year) - 1),
            period_id='Q4',
            model_id='IT-LIPE'
            ).first()
        
        if model is not None:
            json_result = json.loads(model.json_pdf)
            vp14_1 = json_result.get('VP14-1', '').replace(",", ".")
            vp14_2 = json_result.get('VP14-2', '').replace(",", ".")

            if vp14_1:
                prev_vp14_1 = float(vp14_1)
            if vp14_2:
                prev_vp14_2 = float(vp14_2)

    # trimestre anterior de este año
    else:
        if periodo == '4T':
            acconto = PresentedModel.objects.filter(
                seller_id=seller.pk,
                year=year,
                period_id='0A',
                model_id='IT-ACCONTO'
                ).first()
            if acconto and acconto.status.code == 'presented':
                json_result = json.loads(acconto.json_pdf)
                total_str = json_result.get("total", "").replace(",", ".")

                if total_str and float(total_str) >= 103.29:
                    acconto_iva = float(total_str)

        model = PresentedModel.objects.filter(
            seller_id=seller.pk,
            year=year,
            period_id=str(f"Q{period}"),
            model_id='IT-LIPE'
            ).first()
        
        if model is not None:
            json_result = json.loads(model.json_pdf)
            vp14_1 = json_result.get('VP14-1', '').replace(",", ".")
            vp14_2 = json_result.get('VP14-2', '').replace(",", ".")

            if vp14_1:
                prev_vp14_1 = float(vp14_1)
            if vp14_2:
                prev_vp14_2 = float(vp14_2)

    # Obtener Datos de la base de datos
    sql = f"SELECT func_calc_model_it_lipe({seller.pk}, {year}, {first_month}, {latest_month}, {prev_vp14_1}, {prev_vp14_2}, {acconto_iva});"

    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        # JSON PDF
        if codice_fiscale == 'None' or codice_fiscale is None:
            codice_fiscale = ''

        jsonValues = {
            "Nombre_empresa_pag1": nombre,
            "numero_VAT_pag1": codice_fiscale,
            "Nombre_empresa_pag2": nombre,
            "numero_VAT_pag2": codice_fiscale,
            "codice_fiscale_pag1": codice_fiscale,
            "anno": anno,
            "partita_iva": partita_iva,
            "codice_fiscale_dichiarante": codice_fiscale_dec,
            "codice_carica": codice_carica,
            "codice_fiscale_societa": codice_fiscale_soc_dichiarante,
            "firma_dichiarante": firma,
            "codice_fiscale_incaricato": codice_fiscale_incaricato,
            "impegno_presentazione": impegno_pres,
            "fecha_impegno": data_impegno_pres,
            "firma_incaricato": firma_incaricato,
            "Nombre_empresa_pag3": nombre,
            "numero_VAT_pag3": codice_fiscale,
            "codice_fiscale_pag2": codice_fiscale,
            "numero_modelo": '01',
            "VP1_trimestre": trimestre,
            "VP2": str(json_result['VP2']).replace(".", ",").replace("-", ""),
            "VP3": str(json_result['VP3']).replace(".", ",").replace("-", ""),
            "VP4": str(json_result['VP4']).replace(".", ",").replace("-", ""),
            "VP5": str(json_result['VP5']).replace(".", ",").replace("-", ""),
            "VP6-1": str(json_result['VP6_1']).replace(".", ",").replace("-", ""),
            "VP6-2": str(json_result['VP6_2']).replace(".", ",").replace("-", ""),
            "VP7": str(json_result['VP7']).replace(".", ",").replace("-", ""),
            "VP8": str(json_result['VP8']).replace(".", ",").replace("-", ""),
            "VP9": str(json_result['VP9']).replace(".", ",").replace("-", ""),
            "VP12": str(json_result['VP12']).replace(".", ",").replace("-", ""),
            "VP13-1": str(json_result['VP13_1']).replace(".", ",").replace("-", ""),
            "VP13-2": str(json_result['VP13_2']).replace(".", ",").replace("-", ""),
            "VP14-1": str(json_result['VP14_1']).replace(".", ",").replace("-", ""),
            "VP14-2": str(json_result['VP14_2']).replace(".", ",").replace("-", ""),
        }

        if periodo == '4T':
            jsonValues.update({
                "VP12": '',
                "VP14-1": '',
                "VP14-2": '',
                "VP12_hide": str(json_result['VP12']).replace(".", ",").replace("-", ""),
                "VP14_1_hide": str(json_result['VP14_1']).replace(".", ",").replace("-", ""),
                "VP14_2_hide": str(json_result['VP14_2']).replace(".", ",").replace("-", "")
            })
            json_str = json.dumps(jsonValues)
            writer.add_metadata({"/JsonValues": json_str})

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)
            writer.update_page_form_field_values(writer.pages[1], jsonValues)
            writer.update_page_form_field_values(writer.pages[2], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_IT_{year}_{periodo}_LIPE.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModelACCONTO(seller, writer, year, periodo):
    lipe = PresentedModel.objects.filter(
        seller_id=seller.pk,
        year=(int(year) - 1),
        period_id='Q4',
        model_id='IT-LIPE'
        ).first()
    
    acconto = '0,0'
    vp14 = '0,0'
    vp6 = '0,0'

    if lipe is not None and lipe.json_pdf is not None and lipe.json_pdf != '':
        json_result = json.loads(lipe.json_pdf)
        # acconto = float(json_result['VP14-1'].replace(",", ".")) * 0.88
        acconto = 0
        if json_result['VP6-1'] is not None and json_result['VP6-1'] != '':
            acconto = round(float(json_result['VP6-1'].replace(",", ".")) * 0.88, 2)    
        acconto = str(acconto).replace(".", ",")

        vp6 = json_result['VP6-1'] if json_result['VP6-1'] is not None and json_result['VP6-1'] != '' else 0
        vp14 = vp6
        # vp14 = json_result['VP14-1']

    # JSON Base
    jsonValues = {
        "total": acconto,
        "vp14": vp14,
        "vp6": vp6
    }

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_IT_{year}_{periodo}_ACCONTO.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModelVATANNUALE(seller, writer, year, periodo, first_month, latest_month):
    # Obtener Datos Basicos (NIF, Nombre, Ejercicio/Año)
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()
    representative = None
    partita_iva = ''
    if sellervat is not None:
        representative = Representative.objects.filter(
            pk=sellervat.vat_representative_id, type_representation = 'legal_representative').first() if sellervat.vat_representative_id is not None else ''
        partita_iva = sellervat.vat_number.replace("IT", "") if sellervat.vat_number is not None else ''

    codice_fiscale = ''
    cognome = ''
    nome = ''
    provincia = ''
    sesso_m = ''
    sesso_f = ''
    giorno = ''
    mese = ''
    anno = ''
    comune_di_nascita = ''
    denominazione = ''
    codice_fiscale_del_sottoscrittore = ''
    codice_fiscale_societa_dichiarante = ''
    codice_carica = ''
    cognome_diacharante_diverso = ''
    nome_diacharente_diverso = ''
    sesso_m_diacharante_diverso = ''
    sesso_f_diacharante_diverso = ''
    giorno_diacharante_diverso = ''
    mese_diacharante_diverso = ''
    anno_diacharante_diverso = ''
    comune_di_nascita_diacharante_diverso = ''
    situazioni_particolari_firma = ''
    natura_giuridica = ''

    if seller.legal_entity == 'self-employed':
        cognome = seller.last_name.upper() if seller.last_name is not None else ''
        nome = seller.first_name.upper() if seller.first_name is not None else ''
        natura_giuridica = '00'

        if seller.gender == 'M':
            sesso_m = 'X'
        elif seller.gender == 'F':
            sesso_f = 'X'

        if seller.birthdate_seller is not None:
            giorno = "{:02d}".format(seller.birthdate_seller.day)
            mese = "{:02d}".format(seller.birthdate_seller.month)
            anno = seller.birthdate_seller.year

        comune_di_nascita = seller.birth_country.name.replace('España',
                                                              'SPAGNA').upper() if seller.birth_country is not None else ''
        provincia = 'EE'
        situazioni_particolari_firma = f"{cognome} {nome}"

        if sellervat is not None and sellervat.codice_fiscale is not None:
            codice_fiscale = sellervat.codice_fiscale.replace("IT", "")
        if sellervat is not None and sellervat.it_representation_type is not None:
            if sellervat.it_representation_type == '1':
                situazioni_particolari_firma = f"{representative.last_name.upper()} {representative.first_name.upper()}"
            elif sellervat.it_representation_type == '2':
                situazioni_particolari_firma = 'ZAMORA TORRIJOS ALBERTO'


    else:
        denominazione = seller.name
        natura_giuridica = '36'
        if sellervat is not None:
            if sellervat.vat_number is not None:
                codice_fiscale = sellervat.vat_number.replace("IT", "")

            if sellervat.it_representation_type is not None and sellervat.it_representation_type == '1':
                codice_fiscale_societa_dichiarante = partita_iva
                codice_carica = '1'
                codice_fiscale_del_sottoscrittore = representative.codice_fiscale
                cognome_diacharante_diverso = representative.last_name.upper() if representative.last_name is not None else ''
                nome_diacharente_diverso = representative.first_name.upper() if representative.first_name is not None else ''
                if representative.gender == 'M':
                    sesso_m_diacharante_diverso = 'X'
                elif representative.gender == 'F':
                    sesso_f_diacharante_diverso = 'X'

                if representative.birthdate is not None:
                    giorno_diacharante_diverso = "{:02d}".format(representative.birthdate.day)
                    mese_diacharante_diverso = "{:02d}".format(representative.birthdate.month)
                    anno_diacharante_diverso = representative.birthdate.year

                comune_di_nascita_diacharante_diverso = representative.birth_country.name.replace('España',
                                                                                                  'SPAGNA').upper() if representative.birth_country is not None else ''
                situazioni_particolari_firma = f"{representative.last_name.upper()} {representative.first_name.upper()}" if representative.last_name is not None and representative.first_name is not None else ''
            elif sellervat.it_representation_type is not None and sellervat.it_representation_type == '2':
                codice_fiscale_del_sottoscrittore = '****************'
                codice_fiscale_societa_dichiarante = '13007250965'
                codice_carica = '6'
                cognome_diacharante_diverso = 'ZAMORA TORRIJOS'
                nome_diacharente_diverso = 'ALBERTO'
                sesso_m_diacharante_diverso = 'X'
                giorno_diacharante_diverso = '25'
                mese_diacharante_diverso = '07'
                anno_diacharante_diverso = '1988'
                comune_di_nascita_diacharante_diverso = 'SPAGNA'
                situazioni_particolari_firma = 'ZAMORA TORRIJOS ALBERTO'

    # Obtener Datos de la base de datos
    sql = f"SELECT func_calc_model_iva_annuale_it({seller.pk}, {year}, {first_month}, {latest_month});"
    json_result = exec_sql(sql)

    if (json_result != None and json_result != ""):
        # print("json_result: ", json_result)

        jsonValues = {"page0_codice_fiscale": codice_fiscale,
                    "page0_partita_iva": partita_iva,
                    "page0_cognome": cognome,
                    "page0_nome": nome,
                    "page0_sesso_m": sesso_m,
                    "page0_sesso_f": sesso_f,
                    "page0_giorno": giorno,
                    "page0_mese": mese,
                    "page0_anno": anno,
                    "page0_comune_di_nascita": comune_di_nascita,
                    "page0_provincia": provincia,
                    "page0_denominazione": denominazione,
                    "page0_codice_fiscale_del_sottoscrittore": codice_fiscale_del_sottoscrittore,
                    "page0_codice_fiscale_societa_dichiarante": codice_fiscale_societa_dichiarante,
                    "page0_codice_carica": codice_carica,
                    "page0_natura_giuridica": natura_giuridica,
                    "page0_cognome_diacharante_diverso": cognome_diacharante_diverso,
                    "page0_nome_diacharente_diverso": nome_diacharente_diverso,
                    "page0_sesso_f_diacharante_diverso": sesso_f_diacharante_diverso,
                    "page0_sesso_m_diacharante_diverso": sesso_m_diacharante_diverso,
                    "page0_giorno_diacharante_diverso": giorno_diacharante_diverso,
                    "page0_mese_diacharante_diverso": mese_diacharante_diverso,
                    "page0_anno_diacharante_diverso": anno_diacharante_diverso,
                    "page0_comune_di_nascita_diacharante_diverso": comune_di_nascita_diacharante_diverso,
                    "page0_numero_di_moduli": "1",
                    "page0_Invio_avviso_telematico": "X",
                    "page0_Invio_altre_comunicazioni": "X",
                    "page0_situazioni_particolari_firma": situazioni_particolari_firma,
                    "page0_codice_fiscale_dell_incaricato": "13007250965",
                    "page0_soggetto_che_ha_predisposto_la_dichiarazione": "2",
                    "page0_ricezione_avviso_telematico": "X",
                    "page0_ricezione_altre_comunicazioni_telematiche": "X",
                    "page0_data_dell_impegno_giorno": "{:02d}".format(date.today().day),
                    "page0_data_dell_impegno_mese": "{:02d}".format(date.today().month),
                    "page0_data_dell_impegno_anno": date.today().year,
                    "page0_firma_dell_incaricato": "ZAMORA TORRIJOS ALBERTO",
                    "page1_codice_fiscale": codice_fiscale,
                    "page1_mod_n": "1",
                    "page1_VA2": "479110",
                    "page3_codice_fiscale": codice_fiscale,
                    "page3_mod_n": "1",
                    "page3_VE20_1": str(json_result['VE20_1']).replace(".", ","),
                    "page3_VE20_2": str(json_result['VE20_2']).replace(".", ","),
                    "page3_VE21_1": str(json_result['VE21_1']).replace(".", ","),
                    "page3_VE21_2": str(json_result['VE21_2']).replace(".", ","),
                    "page3_VE22_1": str(json_result['VE22_1']).replace(".", ","),
                    "page3_VE22_2": str(json_result['VE22_2']).replace(".", ","),
                    "page3_VE23_1": str(json_result['VE23_1']).replace(".", ","),
                    "page3_VE23_2": str(json_result['VE23_2']).replace(".", ","),
                    "page3_VE24_1": str(json_result['VE24_1']).replace(".", ",").replace("-", ""),
                    "page3_VE24_2": str(json_result['VE24_2']).replace(".", ",").replace("-", ""),
                    "page3_VE26": str(json_result['VE26']).replace(".", ",").replace("-", ""),
                    "page3_VE33": str(json_result['VE33']).replace(".", ",").replace("-", ""),
                    "page3_VE50": str(json_result['VE50']).replace(".", ",").replace("-", ""),
                    "page4_codice_fiscale": codice_fiscale,
                    "page4_mod_n": "1",
                    "page4_VF13_1": str(json_result['VF13_1']).replace(".", ",").replace("-", ""),
                    "page4_VF13_2": str(json_result['VF13_2']).replace(".", ",").replace("-", ""),
                    "page4_VF25_1": str(json_result['VF25_1']).replace(".", ",").replace("-", ""),
                    "page4_VF25_2": str(json_result['VF25_2']).replace(".", ",").replace("-", ""),
                    "page4_VF27": str(json_result['VF27']).replace(".", ",").replace("-", ""),
                    "page4_VF29_3": str(json_result['VF29_3']).replace(".", ",").replace("-", ""),
                    "page5_codice_fiscale": codice_fiscale,
                    "page5_VF60_1": "X",
                    "page5_VF71": str(json_result['VF71']).replace(".", ",").replace("-", ""),
                    "page9_codice_fiscale": codice_fiscale,
                    "page9_mod_n": "1",
                    "page9_VL1": str(json_result['VL1']).replace(".", ",").replace("-", ""),
                    "page9_VL2": str(json_result['VL2']).replace(".", ",").replace("-", ""),
                    "page9_VL3": str(json_result['VL3']).replace(".", ",").replace("-", ""),
                    "page9_VL4": str(json_result['VL4']).replace(".", ",").replace("-", ""),
                    "page9_VL8_1": str(json_result['VL8_1']).replace(".", ",").replace("-", ""),
                    "page9_VL23": str(json_result['VL23']).replace(".", ",").replace("-", ""),
                    "page9_VL25": str(json_result['VL25']).replace(".", ",").replace("-", ""),
                    "page9_VL30_1": str(json_result['VL30_1']).replace(".", ",").replace("-", ""),
                    "page9_VL30_2": str(json_result['VL30_2']).replace(".", ",").replace("-", ""),
                    "page9_VL30_3": str(json_result['VL30_3']).replace(".", ",").replace("-", ""),
                    "page9_VL32": str(json_result['VL32']).replace(".", ",").replace("-", ""),
                    "page9_VL33": str(json_result['VL33']).replace(".", ",").replace("-", ""),
                    "page9_VL36": str(json_result['VL36']).replace(".", ",").replace("-", ""),
                    "page9_VL38": str(json_result['VL38']).replace(".", ",").replace("-", ""),
                    "page9_VL39": str(json_result['VL39']).replace(".", ",").replace("-", ""),
                    "page9_VA": "X",
                    "page9_VE": json_result['VE_CHAR'],
                    "page9_VF": "X",
                    "page9_VL": json_result['VL_CHAR'],
                    "page9_VT": json_result['VT_CHAR'],
                    "page9_VX": json_result['VX_CHAR'],
                    "page11_codice_fiscale": codice_fiscale,
                    "page11_VT1_1": str(json_result['VT1_1']).replace(".", ",").replace("-", ""),
                    "page11_VT1_2": str(json_result['VT1_2']).replace(".", ",").replace("-", ""),
                    "page11_VT1_3": str(json_result['VT1_3']).replace(".", ",").replace("-", ""),
                    "page11_VT1_4": str(json_result['VT1_4']).replace(".", ",").replace("-", ""),
                    "page11_VT2_1": str(json_result['VT2_1']).replace(".", ",").replace("-", ""),
                    "page11_VT2_2": str(json_result['VT2_2']).replace(".", ",").replace("-", ""),
                    "page11_VT11_1": str(json_result['VT11_1']).replace(".", ",").replace("-", ""),
                    "page11_VT11_2": str(json_result['VT11_2']).replace(".", ",").replace("-", ""),
                    "page12_codice_fiscale": codice_fiscale,
                    "page12_VX1": str(json_result['VX1']).replace(".", ",").replace("-", ""),
                    "page12_VX2_1": str(json_result['VX2_1']).replace(".", ",").replace("-", ""),
                    "page12_VX5": str(json_result['VX5']).replace(".", ",").replace("-", ""),

                    }

    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            writer2 = PdfWriter()

            # ADD PAGES FILLs TO WRITER
            written_pages = [0, 1, 3, 4, 5, 9, 11, 12]

            for y in written_pages:
                writer2.add_page(writer.pages[y])

            # SET/UPDATE JSON VALUES TO PDF
            for y in range(8):
                writer2.update_page_form_field_values(writer2.pages[y], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_IT_{year}_{periodo}_VATANNUALE.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount}")
            time.sleep(0.1)

    return response

def calcModelUS5472(seller, writer, year, periodo):
    actual_year = date(int(year), 1, 1)
    model54721120 = PresentedM54721120.objects.filter(seller=seller, year=year).first()
    company_ad = Address.objects.filter(pk=seller.seller_address.pk).first()
    member_ad = Address.objects.filter(pk=model54721120.member_address.pk).first()
    user_member = User.objects.filter(id=seller.user_id).first()
    country = []
    str_country = ''

    json_translation = model54721120.translations

    main_activity_countries = json_translation.get('main_activity_countries', None) or map(lambda x: x.name,
                                                                                           model54721120.main_activity_countries.all())
    str_country = ', '.join(main_activity_countries)

    sale_product = \
        AccountingRecord.objects.filter(seller=seller, pm5472_1120=model54721120.pk, transaction_type='1').aggregate(
            total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    expense_product = \
        AccountingRecord.objects.filter(seller=seller, pm5472_1120=model54721120.pk, transaction_type='2').aggregate(
            total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']

    sale_service = \
        AccountingRecord.objects.filter(seller=seller, pm5472_1120=model54721120.pk, transaction_type='3').aggregate(
            total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    expense_service = \
        AccountingRecord.objects.filter(seller=seller, pm5472_1120=model54721120.pk, transaction_type='4').aggregate(
            total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']

    total_sales = round(sale_product + sale_service, 2)
    total_expenses = round(expense_product + expense_service, 2)

    total_sales_expenses = round(total_sales + total_expenses, 2)

    # print(f'sale_product: {sale_product} | expense_product: {expense_product} | sale_service: {sale_service} | expense_service: {expense_service} | total_sales: {total_sales} | total_expenses: {total_expenses} | total_sales_expenses: {total_sales_expenses}')

    table2 = ['5', '6', '7', '8', '9']
    record = AccountingRecord.objects.filter(seller=seller, pm5472_1120=model54721120.pk,
                                             transaction_type__in=table2).order_by('date')
    total_record = \
    record.aggregate(total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    total_record_acquisition = record.filter(transaction_type='8').aggregate(
        total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    total_record_constitution = record.filter(transaction_type='6').aggregate(
        total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    total_record_contribution = record.filter(transaction_type='5').aggregate(
        total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    total_record_distribution = record.filter(transaction_type='9').aggregate(
        total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']
    total_record_dissolution = record.filter(transaction_type='7').aggregate(
        total=Coalesce(Round(Sum('total_currency'), 2), Value(0, output_field=DecimalField())))['total']

    cas_1f_previous = round(model54721120.casilla_1F_all_previous_years,
                            2) if model54721120.casilla_1F_all_previous_years is not None else 0
    total_1f = round(total_sales_expenses + total_record, 2)
    total_1h = round(total_1f + cas_1f_previous, 2)

    jsonValues = {
        # FORM 1120
        "f1120_name": seller.name.upper(),
        "f1120_address_1": company_ad.address.lower().title(),
        "f1120_address_2": f"{company_ad.address_city.lower().title()}, {company_ad.address_state.lower().title()},{json_translation.get('country_registration', None)}, {company_ad.address_zip.upper()}",
        "f1120_ein": seller.ein,
        "f1120_final_return": "X" if model54721120.is_last_year is True else "",
        "f1120_initial_return": "X" if model54721120.is_first_year is True else "",
        "f1120_sign_title": "SOLE MEMBER",
        "f1120_sign_date": f"{datetime.now().strftime('%m/%d/%Y')}",
        "f1120_date_begining": f"{model54721120.incorporation_date.strftime('%B %d')} " if model54721120.incorporation_date > actual_year else "January 01",
        "f1120_date_ending": "December 31",
        "f1120_year": str(year)[2:],
        "f1120_incorporated_date": model54721120.incorporation_date.strftime("%m/%d/%Y"),
        "f1120_total_assets": str(model54721120.total_assets),
        "f1120_header": "FOREIGN-OWNED U.S. DE" if model54721120.is_foreign_owned is True or model54721120.tax_residence_country.iso_code != 'US' else "",
        # FORM 5472
        "page0_month_1": f"{model54721120.incorporation_date.strftime('%B %d')} " if model54721120.incorporation_date > actual_year else "January 01",
        "page0_day_1": year,
        "page0_month_2": "December 31",
        "page0_day_2": year,
        "page0_field_1a_1": seller.name.upper(),
        "page0_field_1a_2": company_ad.address.lower().title(),
        "page0_field_1a_3": f"{company_ad.address_city.lower().title()}, {company_ad.address_state.lower().title()}, {company_ad.address_zip.upper()}",
        "page0_field_1b": seller.ein,
        "page0_field_1c": str(model54721120.total_assets),
        "page0_field_1d": json_translation.get('desc_main_activity', None) or model54721120.desc_main_activity,
        "page0_field_1e": "541511" if model54721120.type_of_activity == '3' else "45999",
        "page0_field_1f": str(total_1f),
        "page0_field_1g": "1",
        "page0_field_1h": str(total_1h),
        "page0_field_1j": "X" if model54721120.is_first_year is True else "",
        "page0_field_1l": json_translation.get('country_registration', None) or seller.country_registration.name,
        "page0_field_1m": model54721120.incorporation_date.strftime("%m/%d/%Y"),
        "page0_field_1n": json_translation.get('tax_residence_country',
                                               None) or model54721120.tax_residence_country.name,
        "page0_field_1o": str_country,
        "page0_field_2": "X" if model54721120.tax_residence_country.iso_code != 'US' else "",
        "page0_field_3": "X" if model54721120.tax_residence_country.iso_code != 'US' else "",
        "page0_field_4a": f"{user_member.first_name if user_member.first_name else ''} {user_member.last_name + ';' if user_member.last_name else ''} {member_ad.address}, {member_ad.address_city}, {member_ad.address_state}, {member_ad.address_zip}; {member_ad.address_country.iso_code}",
        "page0_field_4b_1": model54721120.itin if model54721120.itin else '',
        "page0_field_4b_3": model54721120.passport if model54721120.is_first_year is True else model54721120.casilla_4b3,
        "page0_field_4c": json_translation.get('member_address_country', None) or member_ad.address_country.name,
        "page0_field_4d": json_translation.get('member_country', None) or model54721120.member_country.name,
        "page0_field_4e": json_translation.get('tax_residence_country',
                                               None) or model54721120.tax_residence_country.name,
        "page1_field_foreign": "X" if model54721120.tax_residence_country.iso_code != 'US' else "",
        "page1_field_usperson": "X" if model54721120.tax_residence_country.iso_code == 'US' else "",
        "page1_field_8a": f"{user_member.first_name if user_member.first_name else ''} {user_member.last_name + ';' if user_member.last_name else ''} {member_ad.address}, {member_ad.address_city}, {member_ad.address_state}, {member_ad.address_zip}; {member_ad.address_country.iso_code}",
        "page1_field_8b_3": model54721120.passport if model54721120.is_first_year is True else model54721120.casilla_4b3,
        "page1_field_8c": json_translation.get('desc_main_activity', None) or model54721120.desc_main_activity,
        "f2_6[0]": "541511" if model54721120.type_of_activity == '3' else "45999",
        "page1_field_8e_3": "X" if model54721120.tax_residence_country.iso_code != 'US' else "",
        "page1_field_8f": json_translation.get('member_address_country', None) or member_ad.address_country.name,
        "page1_field_8g": json_translation.get('tax_residence_country',
                                               None) or model54721120.tax_residence_country.name,

        "page1_field_9": str(sale_product),
        "page1_field_21": str(sale_service),
        "page1_field_22": str(total_sales),

        "page1_field_23": str(expense_product),
        "page1_field_35": str(expense_service),
        "field36": str(total_expenses),

        "page1_field_partv": "X" if record.count() > 0 else "",
        "page2_field_37": "X",
        "page2_field_38a": "X",
        "page2_field_39": "X",
        "page2_field_40a": "X",
        "page2_field_41a": "X",
        "page2_field_42a": "X",
        "page2_field_42b": "X",
        "page2_field_43a": "X",
        "page2_field_45": "X",
        "page2_field_46": "X",
        "page2_field_48c": "X",
    }

    if model54721120.tax_residence_country.iso_code == 'US':
        jsonValues.update({
            "page1_field_9": '',
            "page1_field_21": '',
            "page1_field_22": '',

            "page1_field_23": '',
            "page1_field_35": '',
            "field36": ''
        })

    if model54721120.tax_residence_country.iso_code == 'US':
        jsonValues.update({
            "page1_field_partv": ''
        })

    response = None
    trycount = 0

    signature = seller.signature_image
    while response == None and trycount < 3:
        try:

            # CREATE WRITER
            writer2 = PdfWriter()

            prev_24_5472 = PresentedModel.objects.filter(seller=seller, year=24, model='US-5472').first()
            reader0 = PdfReader(f"/app/muaytax/static/assets/pdf/Form_5472_Reasonable_Cause_Letter.pdf")
            if prev_24_5472 is not None:
                # ADD PAGE FROM reader0 AT THE BEGINNING
                first_page = reader0.pages[0]
                writer2.add_page(first_page)

            # OBTAIN FRONT PAGE FILE (1120)
            if year == "2024":
                reader2 = PdfReader(f"/app/muaytax/static/assets/pdf/f1120_2024.pdf")
            else:
                reader2 = PdfReader(f"/app/muaytax/static/assets/pdf/f1120_2023.pdf")

            reader2 = add_signature_to_pdf(reader2, signature)

            for page in reader2.pages:
                # DIN A4 SIZE (595.276 x 841.890)
                page.mediabox.right = 595.276
                page.mediabox.top = 841.890
                writer2.add_page(page)

            # ADDING MODEL 5472 PAGES
            for x in range(3):
                # DIN A4 SIZE (595.276 x 841.890)
                writer.pages[x].mediabox.right = 595.276
                writer.pages[x].mediabox.top = 841.890
                writer2.add_page(writer.pages[x])

            # Sólo añadimos la tabla si existen transacciones
            if record.count() > 0:
                # ---<< ADDING TABLE PDF>>---
                with tempfile.TemporaryDirectory() as tempdir:
                    data = {
                        "record": record,
                        "total": total_record,
                        "total_acquisition": total_record_acquisition,
                        "total_constitution": total_record_constitution,
                        "total_contribution": total_record_contribution,
                        "total_dissolution": total_record_dissolution,
                        "total_distribution": total_record_distribution,
                        "accounting_translated": json_translation.get('accounting_records', None),
                        "temp_dir": tempdir,
                        "year": year,
                        "seller_name": seller.name,
                        "seller_ein": seller.ein,
                    }

                    pdf_table = create_5472_pdf(**data)

                    # Añadir las páginas al pdf final
                    reader3 = PdfReader(pdf_table)
                    for page in reader3.pages:
                        # DIN A4 SIZE (595.276 x 841.890)
                        page.mediabox.right = 595.276
                        page.mediabox.top = 841.890
                        writer2.add_page(page)

            # SET/UPDATE JSON VALUES TO PDF
            for y in range(4):
                writer2.update_page_form_field_values(writer2.pages[y], jsonValues)


            json_str = json.dumps(jsonValues)
            writer2.add_metadata({
                "/JsonValues": json_str
            })

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_US_{year}_{periodo}_5472-1120.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer2.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount} {e}")
            time.sleep(0.1)

    return response

def calcModelUS7004(seller, writer, year, periodo):
    print(f"Calculando modelo 7004 para {seller.name} {year} {periodo}")
    actual_year = date(int(year), 1, 1)
    company_ad = Address.objects.filter(pk=seller.seller_address.pk).first()
    # incorporation_date = seller.incorporation_llc_date
    incorporation_date = seller.incorporation_llc_date
    if incorporation_date is None:
        model54721120 = PresentedM54721120.objects.filter(seller=seller, year=year).first()
        if model54721120 is not None:
            incorporation_date = model54721120.incorporation_date

    jsonValues = {
        "page0_name": seller.name.upper(),
        "page0_ein": seller.ein,
        "page0_address_1": company_ad.address.lower().title(),
        "page0_address_2": f"{company_ad.address_city.lower().title()}, {company_ad.address_state.lower().title()}, {company_ad.address_zip.upper()}",
        "page0_field_1_code_1": "1",
        "page0_field_1_code_2": "2",
        "page0_field_5a_year_1": year[2:] if incorporation_date <= actual_year else "",
        "page0_field_5a_begin": incorporation_date.strftime('%B') if incorporation_date > actual_year else "",
        "page0_field_5a_year_2": incorporation_date.strftime('%Y')[2:] if incorporation_date > actual_year else "",
        "page0_field_5a_end": "December" if incorporation_date > actual_year else "",
        "page0_field_5a_year_3": year[2:] if incorporation_date > actual_year else "",
        "page0_field_5b_initial": "X" if incorporation_date > actual_year else "",
    }
    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            # SET/UPDATE JSON VALUES TO PDF
            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_US_{year}_{periodo}_7004.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount} {e}")
            time.sleep(0.1)

    return response

def calcModelUSBE15(seller, writer, year, periodo):
    print(f"Calculando modelo BE15 para {seller.name} {year} {periodo}")
    
    modelBE15 = ModelFormBE15.objects.filter(seller=seller, year=year).first()
    user = User.objects.filter(pk=seller.user.pk).first()

    company_ad = Address.objects.filter(pk=seller.seller_address.pk).first()
    member_ad = Address.objects.filter(pk=seller.member_address.pk).first()

    json_translation = modelBE15.translations
    print(f"json_translation: {json_translation}")
    
    
    jsonValues = {
        # Name and address of US business enterprise
        "field_1002": f" {seller.name.upper()}",
        "field_1010": f" {user.first_name.lower().title()} {user.last_name.lower().title()}",
        "field_1003": f" {company_ad.address.lower().title()}",
        "field_1004": f" {company_ad.address_city.lower().title()}",
        "field_0998": f" {company_ad.address_state.lower().title()}",
        "field_1005": f" {company_ad.address_zip}",

        # Contact information
        "field_1000": f" {user.first_name.lower().title()} {user.last_name.lower().title()}",
        "field_1029": f" {member_ad.address.lower().title()}",
        "field_1031": f" {member_ad.address_city.lower().title()},  {member_ad.address_state.lower().title()},  {member_ad.address_zip}",
        "field_1001": f" {seller.contact_phone}",
        "field_1028": f" {user.email}",

        # which sections to comeplete
        "page1_1_yes": "X",
        "page1_2_no": "X",
        "page1_4_no": "X",
        "page1_5_no": "X",

        # basics of claim for exemption
        "field_2109": f"{seller.total_assests}",
        "field_2149": f"{seller.total_turnover}",
        "field_2159": f"{seller.total_benefits}",
        "field_2114": f"{seller.total_liabilities}",
        "field_1163": f"{json_translation.get('desc_main_activity', None)}" or f" {seller.desc_main_activity}",
        "field_1164": f" {seller.products_and_services.code}",
        "field_3016": f" {json_translation.get('member_address_country', None)}" or f" {member_ad.address_country.name}",
        "field_3024": " N/A",
        "field_3022_UBO": f" {json_translation.get('member_address_country', None)}" or f" {member_ad.address_country.name}",
        "field_3025": " N/A",
    }
    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            

            # SET/UPDATE JSON VALUES TO PDF
            for y in range(3):
                writer.update_page_form_field_values(writer.pages[y], jsonValues)

            json_str = json.dumps(jsonValues)
            writer.add_metadata({
                "/JsonValues": json_str
            })

            # FILE PATH
            filePath = f"muaytax/media/generated_models/{seller.shortname}_US_{year}_{periodo}_BE15.pdf"

            # WRITE PDF FILE
            with open(filePath, "wb") as output_stream:
                writer.write(output_stream)

            # DELAY 500ms
            time.sleep(0.5)

            # FLATTEN PDF FILE
            fillpdfs.flatten_pdf(filePath, filePath)

            # RESPONSE
            response = FileResponse(open(filePath, 'rb'), content_type='application/pdf')

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount} {e}")
            time.sleep(0.1)

    return response

def calcModelGBVATProof(seller, writer, year, periodo, fromDate=None, toDate=None, dueDate=None, periodKey=None):
    sql = f"SELECT func_calc_model_vat_proof_gb({seller.pk}, '{fromDate}', '{toDate}');"
    print(f"Ejecutando SQL: {sql}")

    first_date_gb_format = datetime.strptime(fromDate, '%Y-%m-%d').strftime('%d/%m/%Y') if fromDate else ''
    last_date_gb_format = datetime.strptime(toDate, '%Y-%m-%d').strftime('%d/%m/%Y') if toDate else ''
    due_date_gb_format = datetime.strptime(dueDate, '%Y-%m-%d').strftime('%d/%m/%Y') if dueDate else ''

    json_result = exec_sql(sql)
    print(f"Resultado SQL: {json_result}")

    # Si estamos en desarrollo y no hay datos, usamos datos de prueba
    if json_result is None or json_result == "":
        if settings.DEBUG:
            print("Usando datos de prueba para VAT-PROOF en desarrollo")
            json_result = {
                'box_1': 1000.00,  # Ventas con IVA estándar
                'box_2': 200.00,   # Ventas con IVA reducido 
                'box_3': 50.00,    # Ventas exentas
                'box_4': 200.00,   # IVA sobre ventas
                'box_5': 100.00,   # IVA sobre compras
                'box_6': 10,       # Total de ventas UE
                'box_7': 5,        # Total de compras UE
                'box_8': 15,       # Total de transacciones
                'box_9': 8,        # Total de transacciones con IVA
            }
        else:
            print("Usando valores cero para VAT-PROOF en producción")
            json_result = {
                'box_1': 0.00,
                'box_2': 0.00,
                'box_3': 0.00,
                'box_4': 0.00,
                'box_5': 0.00,
                'box_6': 0,
                'box_7': 0,
                'box_8': 0,
                'box_9': 0,
            }


        converter = CurrencyConverter()
        exchange_rate = converter.get_single_exchange_rate(
            base_currency='EUR',
            target_currency='GBP'
        )
        if exchange_rate.error:
            print(f"Error al obtener el tipo de cambio: ")
            return None
        
        for key, value in json_result.items():
            json_result[key] = round(value * exchange_rate.rate, 2)


    jsonValues = {
        'BUSINESS_NAME': f'{seller.name}',
        'PERIOD_KEY': f'{periodKey}',
        'DATE_FROM': f'{first_date_gb_format}',
        'DATE_TO': f'{last_date_gb_format}',
        'DATE_DUE': f'{due_date_gb_format}',
        'BOX_1': str(json_result['box_1']).replace(".", ","),
        'BOX_2': str(json_result['box_2']).replace(".", ","),
        'BOX_3': str(json_result['box_3']).replace(".", ","),
        'BOX_4': str(json_result['box_4']).replace(".", ","),
        'BOX_5': str(json_result['box_5']).replace(".", ","),
        'BOX_6': int(json_result['box_6']),
        'BOX_7': int(json_result['box_7']),
        'BOX_8': int(json_result['box_8']),
        'BOX_9': int(json_result['box_9']),
    }
    response = None
    trycount = 0
    while response == None and trycount < 3:
        try:

            writer.update_page_form_field_values(writer.pages[0], jsonValues)

            writer.add_metadata({
                "/JsonValues": json.dumps(jsonValues),
                "/Producer": "MuayTax VAT-PROOF Generator",
                "/Creator": "MuayTax",
                "/Title": f"VAT Return {periodKey}"
            })

            os.makedirs('muaytax/media/generated_models', exist_ok=True)
            
            filename = f"{seller.shortname}_GB_{year}_{periodo}_VAT_PROOF.pdf"
            file_path = os.path.join('muaytax/media/generated_models', filename)
            relative_url = f'/media/generated_models/{filename}'

            with open(file_path, "wb") as output_stream:
                writer.write(output_stream)

            time.sleep(0.5)
            fillpdfs.flatten_pdf(file_path, file_path)

            response = FileResponse(
                open(file_path, 'rb'),
                content_type='application/pdf',
                as_attachment=False,
                filename=filename
            )
            
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            response['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')
            response['File-Path'] = relative_url
            response['File-Name'] = filename
            response['Access-Control-Expose-Headers'] = 'Content-Disposition, Last-Modified, File-Path, File-Name'

        except Exception as e:
            trycount += 1
            print(f"Error al procesar el PDF: {trycount} {e}")
            time.sleep(0.1)

    return response

def generar_documento_francia(seller, writer, tipo_documento, year=None, periodo=None):
    # Verificar firma
    if not hasattr(seller, 'signature_image') or not seller.signature_image:
        return JsonResponse({'message': 'Es necesario tener una firma registrada para generar el documento.'}, status=400)

    address = seller.seller_address
    if not address:
        # Devolver JsonResponse en lugar de HttpResponse
        return JsonResponse({'message': 'Es necesario que el vendedor tenga una dirección registrada para generar el documento.'}, status=400)

    company_address = f"{address.address}, {address.address_city}, {address.address_state}, {address.address_zip}"

    json_values = {
        'company_name': seller.name,
        'company_address': company_address,
        'today_date': date.today().strftime("%d/%m/%Y"),
        'admin_name': f"{seller.first_name} {seller.last_name}",
        'MONTH_PLUS_YEAR': date.today().strftime("%m/%Y"),
        # 'client_signature': ''
    }

    # Campos adicionales específicos del mandato
    if tipo_documento == "MANDATO":
        seller_vat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='FR').first()
        seller_vat_es = SellerVat.objects.filter(seller=seller, vat_country__iso_code='ES').first()
        # Obtener dirección del banco antes de usarla
        bank_address = seller.seller_bank_address

        required_mandato_fields = {
            'Fecha de nacimiento': seller.birthdate_seller,
            'IBAN': seller.iban,
            'SWIFT/BIC': seller.swift,
            'Nombre del banco': seller.bank_name,
            'Dirección del banco': bank_address,
            'Número de IVA francés': seller_vat.vat_number if seller_vat else None,
            'Número IVA español': seller_vat_es.vat_number if seller_vat_es else None,
        }

        missing_mandato_fields = [field for field, value in required_mandato_fields.items() if not value]
        if missing_mandato_fields:
            # Devolver JsonResponse
            return JsonResponse({
                'message': f'Es necesario completar los siguientes datos del mandato: {", ".join(missing_mandato_fields)}'
            }, status=400)

        bank_address_formatted = f"{bank_address.address}, {bank_address.address_city}, {bank_address.address_state}, {bank_address.address_zip}"

        # Aquí se adapta el diccionario para que use los nombres que espera el PDF del mandato
        json_values.update({
            'adminName': f"{seller.first_name} {seller.last_name}",
            'companyName': seller.name,
            'companyAddress': company_address,
            'spanishTaxId': seller.nif_registration,  # si aplica
            'spanishVATNumber': seller_vat_es.vat_number,
            'bankNameAndAdress': f"{seller.bank_name}, {bank_address_formatted}",
            'iban': seller.iban,
            'swiftOrBic': seller.swift
        })

    for page in writer.pages:
        writer.update_page_form_field_values(page, json_values)

    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
        writer.write(temp_file)
        temp_file_path = temp_file.name

    from muaytax.utils.pdf_signer import add_signature_to_pdf

    signature_path = getattr(seller.signature_image, 'path', None)

    filename_prefix = f"FR-{tipo_documento}_{seller.shortname}_{year}_{periodo}"

    if signature_path:
        signed_file_path = add_signature_to_pdf(temp_file_path, signature_path, field_name="client_signature")
        final_file_path = signed_file_path if signed_file_path else temp_file_path
    else:
        final_file_path = temp_file_path

    response = FileResponse(open(final_file_path, 'rb'), content_type='application/pdf')
    suffix = "_signed" if final_file_path != temp_file_path else ""
    response['Content-Disposition'] = f'inline; filename="{filename_prefix}{suffix}.pdf"'

    os.unlink(temp_file_path)
    if final_file_path != temp_file_path:
        os.unlink(final_file_path)

    return response


def calcModelcontrato_francia(seller, writer, year=None, periodo=None, first_month=None, latest_month=None):
    return generar_documento_francia(seller, writer, "CONTRATO", year, periodo)


def calcModelmandato_francia(seller, writer, year=None, periodo=None, first_month=None, latest_month=None):
    return generar_documento_francia(seller, writer, "MANDATO", year, periodo)
