import json
from django.db import connection
from muaytax.app_sellers.models import <PERSON>llerVat, SellerYieldRecord, SellerVatActivity
from muaytax.app_representatives.models.representative import Representative
from muaytax.dictionaries.models import Model
from muaytax.utils.txt_models import set_transactions_369, set_transactions_349, check_nif_seller
from muaytax.utils.calc_models import exec_sql, exec_sql_secure , SQL_model_dict
from muaytax.app_sellers.vat_validation_utils import check_vat_vies, validate_vat_number_by_country


def check_name_seller(seller, error_messages):
    url = f'<a href="/sellers/{seller.shortname}/information">Ir a Información fiscal</a>'
    explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '
    if seller.legal_entity == 'self-employed':
        if (seller.first_name is None or seller.first_name == '') and (
            seller.last_name is None or seller.last_name == ''):
            error_messages.append({'error': f'El vendedor no tiene nombre y apellidos en su registro (campo Nombre y Apellidos del seller). {explain} {url}'})
            return error_messages

def check_nif_vatCountry(seller, sellervat, error_messages):
    url_vat = f'<a href="/sellers/{seller.shortname}/vat">Ir a listado de países IVA</a>'
    explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '

    if sellervat is None or sellervat.vat_number is None:
        error_messages.append({'error': f'El número de IVA no está asignado. {explain} {url_vat}'})
        return error_messages

    if check_vat_vies(sellervat.vat_number, 'IT') is False:
        if validate_vat_number_by_country('IT', sellervat.vat_number) is False:
            error_messages.append({'error': f'El número de IVA no es válido. <br>N.IVA encontrado: {sellervat.vat_number}.<br>{explain} {url_vat}'})
            return error_messages

def get_prev_data_model390(seller, year, first_month, latest_month):
    '''
    Función que devuelve los reajustes que se han hecho en el SQL del modelo 390, para mostrarlos en el frontal
    '''
    sql = f"SELECT func_calc_model_es_390({seller.pk}, {year}, {first_month}, {latest_month})"
    json_result = exec_sql(sql)

    if json_result is not None:
        adjustment = str(json_result.get('DIF', '')).replace('.', ',') + '€'
        percentage = json_result.get('DIF_PCT', '').replace('.', ',')
        ca086_old = str(json_result.get('CA086_OLD', '')).replace('.', ',') + '€'
        ca086 = str(json_result.get('CA086', '')).replace('.', ',') + '€'
        ca606_old = str(json_result.get('CA606_OLD', '')).replace('.', ',') + '€'
        first_sum = str(json_result.get('FIRST_SUM', '')).replace('.', ',') + '€'

        response = {'adjustment': adjustment,
                    'percentage': percentage,
                    'ca086_old': ca086_old,
                    'ca606_old': ca606_old,
                    'first_sum': first_sum,
                    'ca086': ca086
                    }
        return response

def model_func_validations(seller, model_id, year, first_month, latest_month, period, error_messages):
    '''
    Función que contiene un diccionario con las funciones de validación de los modelos, esta función se encarga de llamar a la
    función correspondiente al modelo que se le está pasando
    '''
    model_dict = {
        '349': lambda: check_model349(seller, year, first_month, latest_month, error_messages),
        '369': lambda: check_model369(seller, year, first_month, latest_month, error_messages),
        '202': lambda: check_model202(seller, year, period, error_messages),
        'LIPE': lambda: check_lipe(seller, error_messages),
        'VATANNUALE': lambda: check_vat_annualle(seller, error_messages)
    }
    func_model = model_dict.get(model_id)
    if func_model:
        func_model()

def model_type_validations(seller, model_id, year, first_month, latest_month, period, error_messages):
    '''
    Función que valida los datos de los modelos antes de enviarlos a revisión
    '''
    check_name_seller(seller, error_messages)
    model = Model.objects.filter(code__icontains=model_id).first()

    if model.country == 'ES':
        check_nif_seller(seller, error_messages)
        model_func_validations(seller, model_id, year, first_month, latest_month, period, error_messages)

    elif model.country == 'IT':
        sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()
        check_nif_vatCountry(seller, sellervat, error_messages)
        model_func_validations(seller, model_id, year, first_month, latest_month, period, error_messages)


# << ---- MODEL VALIDATIONS FUNCTIONS ---- >>
def check_vat_annualle(seller, error_messages):
    '''
    Función que comprueba los datos necesarios para el modelo VATANNUALE
    '''
    explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '
    url_seller = f'<a href="/sellers/{seller.shortname}/detail">Ir a Datos generales</a>'
    representative = None
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()

    if sellervat is not None:
        url_vat = f'<a href="/sellers/{seller.shortname}/vat/{sellervat.pk}">Ir a su País IVA Italia</a>'        

    if seller.legal_entity == 'self-employed':
        if seller.gender is None:
            error_messages.append(
                {'error': f'El vendedor no tiene un género asignado en su registro. {explain} {url_seller}'})
        if seller.birthdate_seller is None:
            error_messages.append({
                'error': f'El vendedor no tiene una fecha de nacimiento asignada en su registro. {explain} {url_seller}'})
        if seller.birth_country is None:
            error_messages.append({
                'error': f'El vendedor no tiene un país de nacimiento asignado en su registro. {explain} {url_seller}'})
        if sellervat is not None:
            if sellervat.codice_fiscale is None:
                error_messages.append(
                    {'error': f'El vendedor no tiene codice fiscale en su País IVA Italia. {explain} {url_vat}'})
            if sellervat.it_representation_type is None:
                error_messages.append({
                    'error': f'El vendedor no tiene tipo de representación en su País IVA Italia. {explain} {url_vat}'})
            else:
                if sellervat.it_representation_type == '1':
                    representative = Representative.objects.filter(pk=sellervat.vat_representative_id, type_representation = 'legal_representative').first()
                    if representative is None:
                        error_messages.append({
                            'error': f'El vendedor no tiene un representante asignado en su País IVA Italia. {explain} {url_vat}'})
                    else:
                        url_rep = f'<a href="/sellers/{seller.shortname}/representatives/update/{representative.pk}">Ver la información del representante</a>'
                        if representative.last_name is None or representative.first_name is None:
                            error_messages.append({
                                'error': f'El representante no tiene nombre y apellidos en su registro. {explain} {url_rep}'})
    else:
        if sellervat is not None:
            if sellervat.vat_number is None:
                error_messages.append(
                    {'error': f'El vendedor no tiene número de IVA en su País IVA Italia. {explain} {url_vat}'})
            if sellervat.it_representation_type is None:
                error_messages.append({
                    'error': f'El vendedor no tiene tipo de representación en su País IVA Italia. {explain} {url_vat}'})
            else:
                if sellervat.it_representation_type == '1':
                    representative = Representative.objects.filter(pk=sellervat.vat_representative_id, type_representation = 'legal_representative').first()
                    if representative is None:
                        error_messages.append({
                            'error': f'El vendedor no tiene un representante asignado en su País IVA Italia. {explain} {url_vat}'})
                    else:
                        url_rep = f'<a href="/sellers/{seller.shortname}/representatives/update/{representative.pk}">Ver la información del representante</a>'
                        if representative.last_name is None or representative.first_name is None:
                            error_messages.append({
                                'error': f'El representante no tiene nombre y apellidos en su registro. {explain} {url_rep}'})
                        if representative.gender is None:
                            error_messages.append({
                                'error': f'El representante no tiene género asignado en su registro. {explain} {url_rep}'})
                        if representative.birthdate is None:
                            error_messages.append({
                                'error': f'El representante no tiene fecha de nacimiento asignada en su registro. {explain} {url_rep}'})
                        if representative.birth_country is None:
                            error_messages.append({
                                'error': f'El representante no tiene país de nacimiento asignada en su registro. {explain} {url_rep}'})

def check_lipe(seller, error_messages):
    '''
    Función que comprueba los datos necesarios para el modelo LIPE
    '''

    explain = f'Por favor completa la información que falta siguiendo el siguiente enlace. '
    representative = None
    sellervat = SellerVat.objects.filter(seller=seller, vat_country__iso_code='IT').first()

    if sellervat is not None:
        url_vat = f'<a href="/sellers/{seller.shortname}/vat/{sellervat.pk}">Ir a su País IVA Italia</a>'
        if sellervat.it_representation_type is None:
                error_messages.append({
                    'error': f'El vendedor no tiene tipo de representación en su País IVA Italia. {explain} {url_vat}'})
        else:
            if sellervat.it_representation_type == '1':
                representative = Representative.objects.filter(pk=sellervat.vat_representative_id, type_representation = 'legal_representative').first()
                if representative is None:
                    error_messages.append({
                        'error': f'El vendedor no tiene un representante asignado en su País IVA Italia. {explain} {url_vat}'})
                else:
                    url_rep = f'<a href="/sellers/{seller.shortname}/representatives/update/{representative.pk}">Ver la información del representante</a>'
                    if representative.last_name is None or representative.first_name is None:
                        error_messages.append({
                            'error': f'El representante no tiene nombre y apellidos en su registro. {explain} {url_rep}'})

def check_model349(seller, year, first_month, latest_month, error_messages):
    '''
    Función que comprueba los datos necesarios para el modelo 349
    '''
    sql = f"SELECT func_calc_model_es_349({seller.pk}, {year}, {first_month}, {latest_month});"
    dict_json = exec_sql(sql)

    set_transactions_349(seller, year, '', dict_json, error_messages)

def check_model369(seller, year, first_month, latest_month, error_messages):
    '''
    Función que comprueba los datos necesarios para el modelo 369
    '''
    sql = f"SELECT func_calc_model_es_369({seller.pk}, {year}, {first_month}, {latest_month});"
    dict_json = exec_sql(sql)

    error_in_dic = dict_json.get('ERRORS')
    if error_in_dic:
        split = error_in_dic.split("ERROR:")
        for x in range(1, len(split)):
            err = split[x]
            error_messages.append({
                'error': f'{err}'})
    
    set_transactions_369(seller , dict_json,[], [], [], error_messages)

def check_model202(seller, year, period, error_messages):
    '''
    Función que comprueba los datos necesarios para el modelo 202
    '''

    year = int(year) - 2 if period == 'M4' else int(year) - 1
    yieldRecords = SellerYieldRecord.objects.filter(seller=seller, year=year, period=period).exists()

    if not yieldRecords:
        error_messages.append({
            'error': f'No se han encontrado registros de ingresos en el modelo 202 del año anterior en el periodo seleccionado. Por favor completa la información de la tabla "Rendimientos Netos" siguiendo el siguiente enlace: <a href="/sellers/{seller.shortname}/information">Ir a Información fiscal</a>'})
    else:
        vat_activity = SellerVatActivity.objects.filter(sellervat__seller=seller, sellervat__vat_country__iso_code = 'ES')
        if vat_activity:
            if vat_activity.count() == 1:
                vat_activity = vat_activity.first()
            else:
                vat_activity = vat_activity.filter(sellervat_activity_iae__code = 'ES-665').first()
            
            cnae = vat_activity.sellervat_activity_iae.code
            if cnae is None:
                error_messages.append({
                    'error': f'No se ha encontrado el CNAE en la actividad económica del vendedor.</a>'})
        else:
            error_messages.append({
                'error': f'No se ha encontrado la actividad económica con el país España del vendedor.</a>'})


# << ---- MODEL POSITIONS EXCEL CALCS ---- >>
def set_positions_excel(model, json):
    '''
    Función que devuelve función que establece las posiciones en función del modelo
    '''

    positions_dict = None
    dict = {
        '130': lambda: set_positions_excel_130(json),
        '303': lambda: set_positions_excel_303(json),
        '309': lambda: set_positions_excel_309(json),

    }
    funct_positions = dict.get(model)
    if funct_positions:
        positions_dict = funct_positions()
    return positions_dict

def set_positions_excel_303(json):
    '''
    Función que establece las posiciones de los datos en el excel de cálculo desglosado del modelo 303
    '''

    positions = {
            (6, 2): json.get("inv_amount4", {}).get("sum_amount", 0),  
            (7, 2): json.get("inv_amount10", {}).get("sum_amount", 0),  
            (8, 2): json.get("inv_amount21", {}).get("sum_amount", 0),
            (15, 2): json.get("inv_local_refund", {}).get("sum_amount", 0),
            (17, 2): json.get("inv_local_refund", {}).get("sum_vat", 0),
            (19, 2): json.get("inv_eqtax05", {}).get("sum_amount", 0),
            (22, 2): json.get("inv_eqtax140", {}).get("sum_amount", 0),
            (25, 2): json.get("inv_eqtax520", {}).get("sum_amount", 0),
            (28, 2): json.get("inv_local_refund_eqtax", {}).get("sum_amount", 0),
            (30, 2): json.get("inv_local_refund_eqtax", {}).get("sum_vat", 0),
            (32, 2): json.get("inv_intra_sales", {}).get("sum_amount", 0),
            (34, 2): json.get("inv_export_sale_refund", {}).get("sum_amount", 0),
            (36, 2): json.get("inv_sales_marketplace", {}).get("sum_amount", 0),
            (38, 2): json.get("inv_oss_not_ES", {}).get("sum_amount", 0),
            (40, 2): json.get("inv_oss_ES", {}).get("sum_amount", 0),
            (50, 2): json.get("inv_intra_expenses", {}).get("sum_amount", 0),
            (53, 2): json.get("inv_extra", {}).get("reverse_charge_invoices_amount", 0),
            (54, 2): json.get("inv_extra", {}).get("amz_expenses_amount", 0),
            (55, 2): json.get("inv_extra", {}).get("amz_credit_amount", 0),
            (56, 2): json.get("inv_extra", {}).get("no_reverse_charge_common_credit_amount", 0),
            (57, 2): json.get("inv_extra", {}).get("no_reverse_charge_common_expenses_amount", 0),
            (60, 2): json.get("inv_local_expenses", {}).get("sum_amount", 0),
            (62, 2): json.get("inv_local_expenses", {}).get("sum_vat", 0),
            (64, 2): json.get("inv_import_dua", {}).get("sum_vat", 0),
            (69, 2): json.get("inv_local_credit", {}).get("sum_amount", 0),
            (71, 2): json.get("inv_local_credit", {}).get("sum_vat", 0),
            (83, 2): json.get("CA77", 0),
            (84, 2): json.get("prev_ca71", 0),
            (85, 2): json.get("prev_ca87", 0),
            (87, 2): json.get("CA78", 0),
        }
    return positions

def set_positions_excel_130(json):
    '''
    Función que establece las posiciones de los datos en el excel de cálculo desglosado del modelo 130
    '''

    #OBTENER LOS TOTALES DE LOS 309
    array_past_months=get_past_periods_from130(json.get("month_max"))
    period309 = 1
    
    for set_month in array_past_months:
        json_response = SQL_model_dict ({
            'model':'309',
            'seller': json.get("sellerid"),
            'year': json.get("date_year"),
            'first_month': set_month[0],
            'latest_month': set_month[1],
        })
        sql: str = json_response["sql"]
        params: tuple = json_response["params"]     
        json_309 = exec_sql_secure(sql,params)
        if (json_309 != None and json_309 != ""):
            json.update({
                f"309TOTALperiod{period309}": json_309.get("CA24")
            })
            period309 += 1
    positions = {
        (3, 2): json.get("inv_amount_general", {}).get("common_sales_amount", 0), 
        (4, 2): json.get("inv_amount_general", {}).get("amz_sales_amount", 0),
        (6, 2): json.get("inv_vat_surcharge", {}).get("common_sales_vat", 0),
        (7, 2): json.get("inv_vat_surcharge", {}).get("amz_sales_vat", 0),
        (13, 2): json.get("inv_amount_general", {}).get("common_expenses_amount", 0),
        (14, 2): json.get("inv_amount_general", {}).get("amz_expenses_amount", 0),
        (16, 2): json.get("inv_vat_surcharge", {}).get("common_expenses_vat", 0),
        (17, 2): json.get("inv_vat_surcharge", {}).get("amz_expenses_vat", 0),
        (19, 2): json.get("inv_eqtax_surcharge", {}).get("common_eqtax", 0),
        (20, 2): json.get("inv_eqtax_surcharge", {}).get("amz_eqtax", 0),
        (22, 2): json.get("inv_intra_surcharge", {}).get("common_intra_amount_600", 0),
        (23, 2): json.get("inv_intra_surcharge", {}).get("amz_intra_amount_600", 0),
        (25, 2): json.get("inv_intra_surcharge", {}).get("common_intra_amount_other", 0),
        (26, 2): json.get("inv_intra_surcharge", {}).get("common_reverse_charge_amount", 0),
        (28, 2): json.get("inv_intra_surcharge", {}).get("amz_intra_amount_other", 0),
        (29, 2): json.get("inv_intra_surcharge", {}).get("amz_reverse_charge_amount", 0),
        (40, 2): json.get("309TOTALperiod1", 0),
        (41, 2): json.get("309TOTALperiod2", 0),
        (42, 2): json.get("309TOTALperiod3", 0),
        (43, 2): json.get("309TOTALperiod4", 0),
        (46, 2): json.get("CA05", 0),
        (47, 2): json.get("inv_irpf", {}).get("common_irpf", 0),
        (48, 2): json.get("inv_irpf", {}).get("amz_irpf", 0),
        (53, 2): json.get("net_yields_last_year", 0),
        (54, 2): json.get("CA13", 0),
        (56, 2): json.get("CA15", 0),
        (61, 2): json.get("seller_direct_estimation"),
        (62, 2): json.get("SelfEmployed"),
    }
    return positions

def set_positions_excel_309(json):
    '''
    Función que establece las posiciones de los datos en el excel de cálculo desglosado del modelo 309
    '''

    positions = {
        (3, 2): json.get("inv_amount", {}).get("common_expenses_amount", 0),
        (4, 2): json.get("inv_amount", {}).get("amz_expenses_amount", 0),
        (6, 2): json.get("inv_amount", {}).get("common_credit_amount", 0),
        (7, 2): json.get("inv_amount", {}).get("amz_credit_amount", 0),
        (9, 2): json.get("inv_amount", {}).get("common_reverse_charge_amount", 0),
        (10, 2): json.get("inv_amount", {}).get("amz_reverse_charge_amount", 0),
        (14, 2): json.get("inv_eqtax", {}).get("common_expenses_amount", 0),
        (15, 2): json.get("inv_eqtax", {}).get("amz_expenses_amount", 0),
        (17, 2): json.get("inv_eqtax", {}).get("common_credit_amount", 0),
        (18, 2): json.get("inv_eqtax", {}).get("amz_credit_amount", 0),
        (20, 2): json.get("inv_eqtax", {}).get("common_reverse_charge_amount", 0),
        (21, 2): json.get("inv_eqtax", {}).get("amz_reverse_charge_amount", 0),
        (27, 2): json.get("has_reverse_charge", ),

    }
    return positions

def get_past_periods_from130(month_max):
    '''
    Función que devuelve el conjunto de todos los meses pasados para poder calcular el total de los modelos 309
    '''
    past_months = None

    if month_max == 3:
        past_months = [[1, 3]]
    elif month_max == 6:
        past_months = [[1, 3],[4, 6]]
    elif month_max == 9:
        past_months = [[1, 3],[4, 6],[7, 9]]
    elif month_max == 12:
        past_months = [[1, 3],[4, 6],[7, 9],[10, 12]]
    return past_months